# ThinkingBox网络状态管理重构概要

## 1. 架构重构总览

### 1.1 重构动机与目标

**核心问题识别**：
- **网络状态同步缺陷**：AdaptiveStreamClient与MVI状态流之间缺乏可靠的状态同步机制
- **错误恢复机制不完善**：网络断线重连逻辑存在时序问题，未能正确处理连接状态变化期间的数据完整性
- **MVI数据流污染**：网络层错误直接影响Presentation层状态，违反了Clean Architecture的层次分离原则
- **用户体验问题**：网络状态变化时缺乏实时反馈，无法感知连接状态和数据传输进度

**重构目标**：
1. 建立符合Clean Architecture的分层网络状态管理系统
2. 实现网络层与MVI架构的正确集成
3. 提供实时、准确的网络状态反馈机制
4. 增强错误处理和自动恢复能力
5. 优化用户体验，提供及时的网络状态反馈

### 1.2 架构设计原则

**Clean Architecture遵循**：
- **Core层**：网络状态监控基础设施，独立于具体实现
- **Domain层**：网络状态业务逻辑，定义UseCase接口
- **Data层**：网络状态数据管理，实现Repository模式
- **Presentation层**：网络状态UI集成，通过MVI模式管理

**MVI 2.0模式集成**：
- 网络状态作为独立的State属性管理
- 网络操作通过专用Intent处理
- 网络错误通过Effect机制反馈给UI
- 保持单一数据源原则(Single Source of Truth)

### 1.3 技术选型决策

**网络状态监控**：
- 使用现有的`AndroidNetworkMonitor`作为平台监控实现
- 扩展`NetworkMonitor`接口支持连接质量评估
- 集成`AdaptiveStreamClient`的连接状态管理

**状态管理方案**：
- 采用`StateFlow`进行响应式状态推送
- 使用`Result<T>`包装所有网络操作结果
- 实现指数退避重试机制

## 2. 网络层重构详情

### 2.1 AdaptiveStreamClient改进

**当前问题分析**：
```kotlin
// 现有问题：缺乏连接状态管理
override fun getCurrentState(): WsState {
    // Always return Open for HTTP+SSE
    return WsState.Open
}
```

**改进方案**：
1. **连接状态真实反映**：追踪实际的SSE连接状态
2. **心跳检测机制**：定期验证连接可用性
3. **智能重连策略**：基于网络状态的自适应重连
4. **状态变化通知**：向NetworkStateMonitor发送连接状态变化事件

**关键接口扩展**：
```kotlin
interface StreamClientStateProvider {
    val connectionState: StateFlow<ConnectionState>
    fun notifyConnectionStateChange(state: ConnectionState)
}

sealed interface ConnectionState {
    object Idle : ConnectionState
    object Connecting : ConnectionState
    object Connected : ConnectionState
    data class Disconnected(val reason: String) : ConnectionState
    data class Error(val throwable: Throwable) : ConnectionState
}
```

### 2.2 NetworkStateMonitor实现

**接口扩展**：
```kotlin
interface NetworkStateMonitor : NetworkMonitor {
    val streamConnectionState: StateFlow<ConnectionState>
    fun updateStreamConnectionState(state: ConnectionState)
    fun checkConnectivity(): Flow<NetworkResult<Boolean>>
}
```

**实现策略**：
1. **组合监控模式**：结合系统网络监控和应用层连接监控
2. **状态聚合逻辑**：综合评估网络可用性和连接质量
3. **事件分发机制**：向订阅者推送网络状态变化事件

### 2.3 错误处理机制升级

**错误分类与处理**：
```kotlin
sealed interface NetworkError {
    data class ConnectionTimeout(val duration: Long) : NetworkError
    data class ConnectionLost(val reason: NetworkLossReason) : NetworkError
    data class AuthenticationFailure(val code: Int) : NetworkError
    data class ServerError(val code: Int, val message: String) : NetworkError
    data class UnknownError(val throwable: Throwable) : NetworkError
}
```

**重试策略实现**：
```kotlin
class ExponentialBackoffRetryStrategy(
    private val initialDelayMs: Long = 1000L,
    private val maxDelayMs: Long = 30000L,
    private val multiplier: Double = 2.0,
    private val maxRetries: Int = 5
) {
    suspend fun executeWithRetry(action: suspend () -> NetworkResult<T>): NetworkResult<T>
}
```

## 3. MVI集成实现

### 3.1 State结构调整

**扩展ThinkingBoxContract.State**：
```kotlin
@Immutable
data class State(
    // 现有状态属性...
    val messageId: String = "",
    val phases: List<PhaseUi> = emptyList(),
    val isStreaming: Boolean = false,
    
    // 新增网络状态管理
    val networkState: NetworkConnectionState = NetworkConnectionState.Unknown,
    val isConnecting: Boolean = false,
    val connectionError: UiText? = null,
    val isRetrying: Boolean = false,
    val retryAttempt: Int = 0,
    val lastConnectionCheck: Long = 0L,
    
    // 连接质量指标
    val connectionQuality: ConnectionQuality = ConnectionQuality.Unknown,
    val latencyMs: Long = 0L,
    
    // 现有错误状态
    val error: UiText? = null,
    val isLoading: Boolean = false,
) : UiState {
    
    /**
     * 计算是否应该显示网络状态警告
     */
    val shouldShowNetworkWarning: Boolean
        get() = connectionError != null || connectionQuality == ConnectionQuality.Poor
        
    /**
     * 计算是否可以进行网络操作
     */
    val canPerformNetworkOperations: Boolean
        get() = networkState is NetworkConnectionState.Connected && !isRetrying
}

enum class NetworkConnectionState {
    Unknown,
    Disconnected,
    Connecting,
    Connected,
    Reconnecting
}

enum class ConnectionQuality {
    Unknown,
    Excellent,
    Good,
    Fair,
    Poor
}
```

### 3.2 Intent/Effect定义

**网络相关Intent**：
```kotlin
sealed interface Intent : AppIntent {
    // 现有Intent...
    
    // 网络状态管理Intent
    data object CheckNetworkConnection : Intent
    data object RetryConnection : Intent
    data class NetworkStatusChanged(val status: NetworkConnectionState) : Intent
    data class ConnectionQualityChanged(val quality: ConnectionQuality, val latencyMs: Long) : Intent
    
    // 网络操作结果Intent
    data class NetworkConnectionResult(val result: NetworkResult<Boolean>) : Intent
    data class ConnectionRetryResult(val result: NetworkResult<Boolean>, val attempt: Int) : Intent
}
```

**网络相关Effect**：
```kotlin
sealed interface Effect : UiEffect {
    // 现有Effect...
    
    // 网络状态Effect
    data class ShowNetworkError(val error: UiText) : Effect
    data class ShowConnectionRestored : Effect
    data object StartNetworkMonitoring : Effect
    data object StopNetworkMonitoring : Effect
    data class UpdateConnectionIndicator(val state: NetworkConnectionState) : Effect
    
    // 重试相关Effect
    data class ScheduleConnectionRetry(val delayMs: Long, val attempt: Int) : Effect
    data object CancelScheduledRetry : Effect
}
```

### 3.3 Reducer逻辑优化

**网络状态处理逻辑**：
```kotlin
private fun handleNetworkStatusChanged(
    state: State,
    intent: Intent.NetworkStatusChanged
): ReducerResult {
    val newState = state.copy(
        networkState = intent.status,
        isConnecting = intent.status == NetworkConnectionState.Connecting,
        isRetrying = intent.status == NetworkConnectionState.Reconnecting,
        connectionError = if (intent.status == NetworkConnectionState.Connected) null else state.connectionError
    )
    
    val effects = mutableListOf<Effect>()
    
    when (intent.status) {
        NetworkConnectionState.Connected -> {
            effects.add(Effect.ShowConnectionRestored)
            effects.add(Effect.UpdateConnectionIndicator(intent.status))
        }
        NetworkConnectionState.Disconnected -> {
            effects.add(Effect.ScheduleConnectionRetry(delayMs = 2000L, attempt = 1))
        }
        NetworkConnectionState.Connecting -> {
            effects.add(Effect.UpdateConnectionIndicator(intent.status))
        }
        else -> { /* 其他状态处理 */ }
    }
    
    return ReducerResult(newState, effects)
}
```

## 4. 代码变更映射

### 4.1 新增文件清单

**Core-Network模块**：
1. `core-network/src/main/kotlin/com/example/gymbro/core/network/state/NetworkStateMonitor.kt` - 扩展现有接口
2. `core-network/src/main/kotlin/com/example/gymbro/core/network/state/ConnectionState.kt` - 连接状态定义
3. `core-network/src/main/kotlin/com/example/gymbro/core/network/retry/NetworkRetryStrategy.kt` - 重试策略实现
4. `core-network/src/main/kotlin/com/example/gymbro/core/network/protocol/StreamClientStateProvider.kt` - 流客户端状态提供者接口

**ThinkingBox模块**：
1. `features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/domain/usecase/NetworkStatusUseCase.kt` - 网络状态业务逻辑
2. `features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/data/repository/NetworkStatusRepository.kt` - 网络状态数据管理
3. `features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/domain/evaluator/ConnectionQualityEvaluator.kt` - 连接质量评估器

### 4.2 修改文件详情

**核心修改文件**：
1. `core-network/src/main/kotlin/com/example/gymbro/core/network/protocol/AdaptiveStreamClient.kt`
   - 添加连接状态管理
   - 实现心跳检测机制
   - 集成重试策略

2. `features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/presentation/contract/ThinkingBoxContract.kt`
   - 扩展State结构添加网络状态
   - 新增网络相关Intent和Effect
   - 添加状态计算属性

3. `features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/presentation/viewmodel/ThinkingBoxViewModel.kt`
   - 集成网络状态监控
   - 处理网络状态变化事件
   - 实现错误恢复逻辑

4. `features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/presentation/reducer/ThinkingBoxContractReducer.kt`
   - 添加网络状态处理逻辑
   - 实现重试机制
   - 优化错误状态管理

### 4.3 删除废弃代码

**清理项目**：
1. 移除AdaptiveStreamClient中的硬编码状态返回
2. 清理不一致的错误处理逻辑
3. 移除重复的网络检查代码

## 5. 性能优化成果

### 5.1 连接性能提升

**目标指标**：
- 网络连接建立时间：< 3秒 (当前: >5秒)
- 断线重连成功率：> 95% (当前: ~80%)
- 网络状态UI反馈延迟：< 500ms (当前: >2秒)

**优化策略**：
1. **并行连接检测**：同时检查系统网络和应用连接
2. **智能缓存策略**：缓存连接状态避免重复检测
3. **预连接机制**：在网络状态变化时预建立连接

### 5.2 内存使用优化

**内存控制**：
- 内存使用增长：< 10MB
- 网络监控组件生命周期优化
- 状态缓存大小限制

### 5.3 用户体验改善

**UX改进**：
1. **实时状态指示器**：显示当前网络连接状态
2. **智能错误提示**：根据错误类型提供相应的用户指导
3. **后台重连**：用户无感知的自动重连机制

## 6. 测试验证方案

### 6.1 单元测试覆盖

**网络层测试**：
```kotlin
class NetworkStateMonitorTest {
    @Test fun `network state changes are properly propagated`()
    @Test fun `connection retry follows exponential backoff`()
    @Test fun `connection quality is accurately evaluated`()
}

class AdaptiveStreamClientTest {
    @Test fun `connection state reflects actual SSE status`()
    @Test fun `heartbeat detection works correctly`()
    @Test fun `retry strategy is applied on connection failure`()
}
```

**MVI集成测试**：
```kotlin
class ThinkingBoxViewModelTest {
    @Test fun `network status changes update state correctly`()
    @Test fun `retry effects are emitted on connection failure`()
    @Test fun `error recovery clears previous error state`()
}
```

### 6.2 集成测试场景

**网络场景测试**：
1. **网络断线恢复**：验证断网后自动重连机制
2. **弱网环境**：测试低带宽下的连接质量评估
3. **网络切换**：验证WiFi到移动网络的切换处理
4. **长时间连接**：测试长期连接的稳定性

### 6.3 性能回归检查

**性能基准**：
```kotlin
@PerformanceTest
class NetworkPerformanceTest {
    @Test fun `connection establishment within 3 seconds`()
    @Test fun `memory usage stays within 10MB limit`()
    @Test fun `UI responsiveness during network operations`()
}
```

## 7. 风险评估与缓解

### 7.1 技术风险点

**高风险项**：
1. **状态同步复杂性**：多个状态源可能导致不一致
   - **缓解**：使用单一状态源原则，建立清晰的状态更新顺序

2. **重试机制过激**：过度重试可能影响性能
   - **缓解**：实现智能退避策略，设置最大重试次数

3. **内存泄漏风险**：网络监听器未正确释放
   - **缓解**：严格的生命周期管理，使用WeakReference

### 7.2 兼容性考虑

**兼容性保证**：
1. **API兼容性**：保持现有接口不变，通过扩展实现新功能
2. **向后兼容**：新的网络状态管理不影响现有功能
3. **渐进式部署**：支持功能开关，可逐步启用新功能

### 7.3 回滚方案

**回滚策略**：
1. **功能开关**：可通过配置禁用新的网络状态管理
2. **代码回滚**：保留原有实现作为备选方案
3. **数据兼容**：新状态字段可选，不影响现有数据结构

## 8. 后续优化方向

### 8.1 智能网络适配

**未来增强**：
1. **网络质量预测**：基于历史数据预测网络状况
2. **自适应流控**：根据网络质量调整数据传输策略
3. **离线模式支持**：网络不可用时的降级处理

### 8.2 监控与分析

**数据收集**：
1. **网络性能指标**：收集连接时间、重试次数等数据
2. **用户行为分析**：分析网络状态对用户体验的影响
3. **异常检测**：自动识别网络异常模式

### 8.3 跨模块集成

**架构扩展**：
1. **全局网络状态**：为其他模块提供网络状态服务
2. **统一错误处理**：建立应用级的网络错误处理机制
3. **性能优化中心**：集中管理网络相关的性能优化策略

---

## 实施计划

### Phase 1: 核心架构搭建 (1-2天)
- 实现NetworkStateMonitor扩展
- 修改AdaptiveStreamClient连接状态管理
- 基础MVI集成

### Phase 2: 错误处理与重试机制 (1天)
- 实现指数退避重试策略
- 完善错误分类和处理
- 集成到ThinkingBox MVI架构

### Phase 3: 性能优化与测试 (1天)
- 性能指标达标验证
- 单元测试和集成测试
- 文档更新和代码审查

**总预计时间：3-4天**
**风险级别：中等**
**影响范围：ThinkingBox模块，Core-Network模块**