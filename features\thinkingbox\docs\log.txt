SSE init failed
kotlinx.coroutines.JobCancellationException: ProducerCoroutine was cancelled; job=ProducerCoroutine{Cancelling}@eba***-***-****ee

kotlinx.coroutines.JobCancellationException: ProducerCoroutine was cancelled; job=ProducerCoroutine{Cancelling}@eba18ee
📡 SSE连接失败
java.net.SocketException: Socket closed
	at java.net.SocketInputStream.read(SocketInputStream.java:***-***-****)
	at java.net.SocketInputStream.read(SocketInputStream.java:***-***-****)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:***-***-****)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:***-***-****)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:***-***-****)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:***-***-****)
	at okio.InputStreamSource.read(JvmOkio.kt:***-***-****)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:***-***-****)
	at okio.RealBufferedSource.request(RealBufferedSource.kt:***-***-****)
	at okio.RealBufferedSource.require(RealBufferedSource.kt:***-***-****)
	at okio.RealBufferedSource.readHexadecimalUnsignedLong(RealBufferedSource.kt:***-***-****)
	at okhttp3.internal.http1.Http1ExchangeCodec$ChunkedSource.readChunkSize(Http1ExchangeCodec.kt:***-***-****)
	at okhttp3.internal.http1.Http1ExchangeCodec$ChunkedSource.read(Http1ExchangeCodec.kt:***-***-****)
	at okhttp3.internal.connection.Exchange$ResponseBodySource.read(Exchange.kt:***-***-****)
	at okio.RealBufferedSource.select(RealBufferedSource.kt:***-***-****)
	at okhttp3.internal.sse.ServerSentEventReader.processNextEvent(ServerSentEventReader.kt:***-***-****)
	at okhttp3.internal.sse.RealEventSource.processResponse(RealEventSource.kt:***-***-****)
	at okhttp3.internal.sse.RealEventSource.onResponse(RealEventSource.kt:***-***-****)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:***-***-****)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:***-***-****)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:***-***-****)
	at java.lang.Thread.run(Thread.java:***-***-****)

java.net.SocketException: Socket closed
	at java.net.SocketInputStream.read(SocketInputStream.java:188)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:994)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:958)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:873)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:846)
	at okio.InputStreamSource.read(JvmOkio.kt:93)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:153)
	at okio.RealBufferedSource.request(RealBufferedSource.kt:210)
	at okio.RealBufferedSource.require(RealBufferedSource.kt:203)
	at okio.RealBufferedSource.readHexadecimalUnsignedLong(RealBufferedSource.kt:388)
	at okhttp3.internal.http1.Http1ExchangeCodec$ChunkedSource.readChunkSize(Http1ExchangeCodec.kt:437)
	at okhttp3.internal.http1.Http1ExchangeCodec$ChunkedSource.read(Http1ExchangeCodec.kt:416)
	at okhttp3.internal.connection.Exchange$ResponseBodySource.read(Exchange.kt:281)
	at okio.RealBufferedSource.select(RealBufferedSource.kt:233)
	at okhttp3.internal.sse.ServerSentEventReader.processNextEvent(ServerSentEventReader.kt:50)
	at okhttp3.internal.sse.RealEventSource.processResponse(RealEventSource.kt:75)
	at okhttp3.internal.sse.RealEventSource.onResponse(RealEventSource.kt:46)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
	at java.lang.Thread.run(Thread.java:1119)
🎯 [AiCoachViewModel] Processing: ResetStreamingState
