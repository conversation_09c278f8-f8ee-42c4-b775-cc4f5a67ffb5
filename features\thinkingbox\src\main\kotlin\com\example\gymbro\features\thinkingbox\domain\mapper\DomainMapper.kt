package com.example.gymbro.features.thinkingbox.domain.mapper

import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * DomainMapper - 语义事件到业务事件映射器
 *
 * 🎯 核心职责：
 * - 将 StreamingThinkingMLParser 产生的 SemanticEvent 转换为 ThinkingEvent
 * - 管理业务状态转换：Phase ID 分配、Title 缓冲、状态转换
 * - 实现双时序架构的业务逻辑层
 *
 * 🔥 架构原则：
 * - 纯映射器：无副作用，只进行事件转换
 * - 状态管理：维护轻量级映射上下文
 * - 业务逻辑：处理复杂的 XML 标签到业务事件的映射
 */
@Singleton
class DomainMapper @Inject constructor() {

    /**
     * 映射上下文 - 轻量级状态容器
     * 维护映射过程中必要的状态信息
     */
    data class MappingContext(
        val currentPhaseId: String? = null, // 当前活跃的 phase ID
        val lastPhaseId: String? = null, // 最近处理的 phase ID (用于 title 映射)
        val inThinkTag: Boolean = false, // 是否在 <think> 标签内
        val inThinkingTag: Boolean = false, // 是否在 <thinking> 标签内
        val inTitleTag: Boolean = false, // 是否在 <title> 标签内
        val inFinalTag: Boolean = false, // 是否在 <final> 标签内
        val hasPerthinkStarted: Boolean = false, // perthink 是否已开始
        val perthinkCompleted: Boolean = false, // perthink 是否已完成
        val titleBuffer: StringBuilder = StringBuilder(), // title 内容缓冲区
    )

    /**
     * 映射结果
     * 包含转换后的业务事件和更新的上下文
     */
    data class MappingResult(
        val events: List<ThinkingEvent>,
        val context: MappingContext,
    )

    /**
     * 主映射方法 - 将单个语义事件转换为业务事件
     *
     * @param event 输入的语义事件
     * @param context 当前映射上下文
     * @return 映射结果，包含转换后的事件和更新的上下文
     */
    fun mapSemanticToThinking(
        event: SemanticEvent,
        context: MappingContext = MappingContext(),
    ): MappingResult {
        // 记录关键事件的映射
        if (isKeyEvent(event)) {
            Timber.tag("TB-MAPPER").d("🔥 [核心映射] 处理关键事件: ${event::class.simpleName}")
        }

        return when (event) {
            // ===== 直接映射的统一事件 =====

            is SemanticEvent.PreThinkChunk -> {
                MappingResult(
                    events = listOf(ThinkingEvent.PreThinkChunk(event.text)),
                    context = context,
                )
            }

            is SemanticEvent.PreThinkEnd -> {
                MappingResult(
                    events = listOf(ThinkingEvent.PreThinkEnd),
                    context = context.copy(
                        inThinkTag = false,
                        perthinkCompleted = true,
                    ),
                )
            }

            is SemanticEvent.PhaseStart -> {
                Timber.tag("TB-MAPPER").d("🎯 映射 PhaseStart: id=${event.id}, title=${event.title}")
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseStart(event.id, event.title)),
                    context = context.copy(
                        currentPhaseId = event.id,
                        lastPhaseId = event.id,
                        hasPerthinkStarted = if (event.id == "perthink") true else context.hasPerthinkStarted,
                    ),
                )
            }

            is SemanticEvent.PhaseContent -> {
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseContent(event.id, event.content)),
                    context = context,
                )
            }

            is SemanticEvent.PhaseEnd -> {
                Timber.tag("TB-MAPPER").d("🎯 映射 PhaseEnd: id=${event.id}")
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseEnd(event.id)),
                    context = context.copy(
                        currentPhaseId = if (context.currentPhaseId == event.id) null else context.currentPhaseId,
                    ),
                )
            }

            is SemanticEvent.PhaseTitleUpdate -> {
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseTitleUpdate(event.phaseId, event.title)),
                    context = context,
                )
            }

            is SemanticEvent.FinalStart -> {
                MappingResult(
                    events = listOf(ThinkingEvent.FinalStart),
                    context = context.copy(inFinalTag = true),
                )
            }

            is SemanticEvent.FinalChunk -> {
                MappingResult(
                    events = listOf(ThinkingEvent.FinalToken(event.content)),
                    context = context,
                )
            }

            is SemanticEvent.FinalEnd -> {
                MappingResult(
                    events = listOf(ThinkingEvent.FinalEnd),
                    context = context.copy(inFinalTag = false),
                )
            }

            is SemanticEvent.FinalArrived -> {
                // 向后兼容已弃用的事件
                @Suppress("DEPRECATION")
                MappingResult(
                    events = listOf(ThinkingEvent.FinalArrived(event.markdown)),
                    context = context,
                )
            }

            // ===== 复杂 XML 标签事件映射 =====

            is SemanticEvent.TagOpened -> mapTagOpened(event, context)
            is SemanticEvent.TagClosed -> mapTagClosed(event, context)
            is SemanticEvent.TextChunk -> mapTextChunk(event, context)

            // ===== 不需要映射的事件 =====

            is SemanticEvent.RawThinking,
            is SemanticEvent.RawThinkingChunk,
            is SemanticEvent.RawThinkingClosed,
            is SemanticEvent.StreamFinished,
            is SemanticEvent.FunctionCallDetected,
            is SemanticEvent.TokensSnapshot,
            is SemanticEvent.ParseErrorEvent,
            -> {
                // 这些事件不需要映射到 ThinkingEvent
                MappingResult(events = emptyList(), context = context)
            }
        }
    }

    /**
     * 处理 XML 标签开启事件
     * 实现复杂的业务逻辑：状态转换、phase 创建、双时序协调
     */
    private fun mapTagOpened(
        event: SemanticEvent.TagOpened,
        context: MappingContext,
    ): MappingResult {
        return when (event.name) {
            "think" -> {
                Timber.tag("TB-MAPPER").d("🎯 <think> 标签开始 - 创建 perthink phase")
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseStart("perthink", "Bro is thinking")),
                    context = context.copy(
                        currentPhaseId = "perthink",
                        lastPhaseId = "perthink",
                        inThinkTag = true,
                        hasPerthinkStarted = true,
                    ),
                )
            }

            "thinking" -> {
                val events = mutableListOf<ThinkingEvent>()

                // 如果当前在 perthink 阶段，先结束它
                if (context.currentPhaseId == "perthink") {
                    Timber.tag("TB-MAPPER").d("🔚 <thinking> 检测到 - 结束 perthink phase")
                    events.add(ThinkingEvent.PreThinkEnd)
                    events.add(ThinkingEvent.PhaseEnd("perthink"))
                }

                MappingResult(
                    events = events,
                    context = context.copy(
                        inThinkingTag = true,
                        inThinkTag = false,
                        currentPhaseId = null, // 清空，等待正式 phase
                        perthinkCompleted = true,
                    ),
                )
            }

            "phase" -> {
                val phaseId = event.attrs["id"] ?: "unknown"
                val events = mutableListOf<ThinkingEvent>()

                if (phaseId == "perthink") {
                    // 处理 <phase id="perthink">，等同于 <think> 标签
                    Timber.tag("TB-MAPPER").d("🎯 <phase id=\"perthink\"> - 创建 perthink phase")
                    events.add(ThinkingEvent.PhaseStart("perthink", "Bro is thinking"))
                } else {
                    // 正式 phase，先结束当前 phase（如果有）
                    if (context.currentPhaseId != null) {
                        val currentPhaseId = context.currentPhaseId!!
                        Timber.tag("TB-MAPPER").d("🔄 新 phase: $phaseId - 结束当前 phase: $currentPhaseId")

                        if (currentPhaseId == "perthink") {
                            events.add(ThinkingEvent.PreThinkEnd)
                        }
                        events.add(ThinkingEvent.PhaseEnd(currentPhaseId))
                    }

                    // 开始新的正式 phase
                    Timber.tag("TB-MAPPER").d("🎯 开始新 Phase: $phaseId")
                    events.add(ThinkingEvent.PhaseStart(phaseId, null))
                }

                MappingResult(
                    events = events,
                    context = context.copy(
                        currentPhaseId = phaseId,
                        lastPhaseId = phaseId,
                        hasPerthinkStarted = if (phaseId == "perthink") true else context.hasPerthinkStarted,
                    ),
                )
            }

            "title" -> {
                Timber.tag("TB-TITLE").i("🏷️ [Title映射] <title> 标签开始 - 清空缓冲区")
                context.titleBuffer.clear()

                MappingResult(
                    events = emptyList(),
                    context = context.copy(inTitleTag = true),
                )
            }

            "final" -> {
                Timber.tag("TB-MAPPER").d("🔥 <final> 标签开始 - 激活后台渲染")
                MappingResult(
                    events = listOf(ThinkingEvent.FinalStart),
                    context = context.copy(inFinalTag = true),
                )
            }

            else -> {
                Timber.tag("TB-MAPPER").w("未知标签: ${event.name}")
                MappingResult(events = emptyList(), context = context)
            }
        }
    }

    /**
     * 处理 XML 标签关闭事件
     * 实现状态转换、phase 结束、特殊标签处理
     */
    private fun mapTagClosed(
        event: SemanticEvent.TagClosed,
        context: MappingContext,
    ): MappingResult {
        return when (event.name) {
            "think" -> {
                Timber.tag("TB-MAPPER").d("🔚 </think> 标签结束")
                MappingResult(
                    events = emptyList(),
                    context = context.copy(inThinkTag = false),
                )
            }

            "thinking" -> {
                Timber.tag("TB-MAPPER").d("🔚 </thinking> 标签 - 创建 final phase 并结束思考")
                val events = mutableListOf<ThinkingEvent>()

                // 先结束当前 phase（如果有）
                if (context.currentPhaseId != null) {
                    events.add(ThinkingEvent.PhaseEnd(context.currentPhaseId!!))
                }

                // 创建并立即完成 phase id="final" - 唯一职责：关闭思考框
                events.add(ThinkingEvent.PhaseStart("final", "Final Answer"))
                events.add(ThinkingEvent.PhaseEnd("final"))
                events.add(ThinkingEvent.ThinkingEnd)

                MappingResult(
                    events = events,
                    context = context.copy(
                        inThinkingTag = false,
                        currentPhaseId = null,
                    ),
                )
            }

            "phase" -> {
                val phaseId = context.currentPhaseId ?: "unknown"
                Timber.tag("TB-MAPPER").d("🔚 </phase> 标签 - 结束 phase: $phaseId")
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseEnd(phaseId)),
                    context = context.copy(currentPhaseId = null),
                )
            }

            "title" -> {
                val targetPhaseId = context.currentPhaseId ?: context.lastPhaseId
                val completeTitle = context.titleBuffer.toString()

                Timber.tag("TB-TITLE").i("🏷️ [Title映射] </title> 标签结束 - 发送完整标题")
                Timber.tag("TB-TITLE").i("🏷️ [Title映射] 标题: '$completeTitle', targetPhaseId: $targetPhaseId")

                if (targetPhaseId != null && completeTitle.isNotBlank()) {
                    MappingResult(
                        events = listOf(ThinkingEvent.PhaseTitleUpdate(targetPhaseId, completeTitle)),
                        context = context.copy(inTitleTag = false),
                    )
                } else {
                    Timber.tag(
                        "TB-TITLE",
                    ).w("🏷️ [Title映射] 无法发送标题 - targetPhaseId: $targetPhaseId, title: '$completeTitle'")
                    MappingResult(
                        events = emptyList(),
                        context = context.copy(inTitleTag = false),
                    )
                }
            }

            "final" -> {
                Timber.tag("TB-MAPPER").d("🔥 </final> 标签 - 结束后台渲染")
                MappingResult(
                    events = listOf(ThinkingEvent.FinalEnd),
                    context = context.copy(inFinalTag = false),
                )
            }

            else -> {
                Timber.tag("TB-MAPPER").w("未知标签关闭: ${event.name}")
                MappingResult(events = emptyList(), context = context)
            }
        }
    }

    /**
     * 处理文本块事件
     * 根据当前上下文状态将文本分发到对应的业务事件
     */
    private fun mapTextChunk(
        event: SemanticEvent.TextChunk,
        context: MappingContext,
    ): MappingResult {
        return when {
            // perthink 阶段的文本 - 同时发送给 header 和 phase
            context.currentPhaseId == "perthink" && !context.perthinkCompleted -> {
                Timber.tag("TB-MAPPER").d("📝 perthink 文本: ${event.text.take(50)}...")
                MappingResult(
                    events = listOf(
                        ThinkingEvent.PreThinkChunk(event.text), // 给 Header 显示
                        ThinkingEvent.PhaseContent("perthink", event.text), // 给 Phase 存储
                    ),
                    context = context,
                )
            }

            // 正式 phase 的内容
            context.currentPhaseId != null && context.inThinkingTag -> {
                Timber.tag("TB-MAPPER").d("📝 Phase ${context.currentPhaseId} 内容: ${event.text.take(50)}...")
                MappingResult(
                    events = listOf(ThinkingEvent.PhaseContent(context.currentPhaseId!!, event.text)),
                    context = context,
                )
            }

            // title 标签内的文本 - 缓冲完整标题
            context.inTitleTag -> {
                val targetPhaseId = context.currentPhaseId ?: context.lastPhaseId

                if (targetPhaseId != null) {
                    context.titleBuffer.append(event.text)
                    Timber.tag(
                        "TB-TITLE",
                    ).i("🏷️ [Title缓冲] 追加文本: '${event.text}' - 总长度: ${context.titleBuffer.length}")

                    // 不立即发送事件，等待 </title> 标签
                    MappingResult(events = emptyList(), context = context)
                } else {
                    Timber.tag("TB-TITLE").w("🏷️ [Title缓冲] 无法确定目标 phaseId")
                    MappingResult(events = emptyList(), context = context)
                }
            }

            // final 标签内的文本
            context.inFinalTag -> {
                Timber.tag("TB-MAPPER").d("📄 Final chunk: ${event.text.take(50)}...")
                MappingResult(
                    events = listOf(ThinkingEvent.FinalToken(event.text)),
                    context = context,
                )
            }

            else -> {
                // 被忽略的文本 - 记录详细状态用于调试
                Timber.tag("TB-MAPPER").w(
                    "❌ [文本忽略] '${event.text.take(50)}...', " +
                        "状态: currentPhaseId=${context.currentPhaseId}, " +
                        "inThinkTag=${context.inThinkTag}, " +
                        "inThinkingTag=${context.inThinkingTag}, " +
                        "inTitleTag=${context.inTitleTag}, " +
                        "perthinkCompleted=${context.perthinkCompleted}",
                )
                MappingResult(events = emptyList(), context = context)
            }
        }
    }

    /**
     * 判断是否为关键事件（需要特别记录日志）
     */
    private fun isKeyEvent(event: SemanticEvent): Boolean {
        return when (event) {
            is SemanticEvent.PreThinkChunk,
            is SemanticEvent.PhaseStart,
            is SemanticEvent.PhaseEnd,
            is SemanticEvent.FinalStart,
            is SemanticEvent.FinalEnd,
            -> true
            else -> false
        }
    }
}
