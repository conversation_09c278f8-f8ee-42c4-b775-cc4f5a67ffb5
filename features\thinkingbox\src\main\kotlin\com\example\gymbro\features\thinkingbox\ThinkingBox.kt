package com.example.gymbro.features.thinkingbox

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.gymbro.core.ai.tokenizer.TokenizerService
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.thinkingbox.domain.interfaces.UiState
import com.example.gymbro.features.thinkingbox.internal.presentation.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.internal.presentation.ui.*
import com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel.ThinkingBoxViewModel
import timber.log.Timber
import kotlin.time.Duration.Companion.milliseconds

/**
 * ThinkingBoxRoot - The internal, MVI-wired root Composable for the ThinkingBox feature.
 * This is the single entry point that connects the ViewModel to the UI.
 * It is not intended for direct public use; use the exported `ThinkingBox` from `ThinkingBoxExports.kt`.
 */
@Composable
internal fun ThinkingBoxRoot(
    messageId: String,
    modifier: Modifier = Modifier,
    tokenizerService: TokenizerService? = null,
    onDataReady: ((ThinkingBoxData) -> Unit)? = null,
    viewModel: ThinkingBoxViewModel = hiltViewModel(),
) {
    // 初始化
    LaunchedEffect(messageId) {
        println("🔥 [LaunchedEffect] 开始执行，messageId=$messageId")
        Timber.tag("TB-LIFECYCLE").d("🔥 [MVI初始化] ThinkingBoxRoot LaunchedEffect 开始执行，messageId=$messageId")
        Timber.tag("TB-LIFECYCLE").d("🔥 [MVI初始化] 即将发送 Initialize Intent 到 ThinkingBoxViewModel")
        viewModel.dispatch(ThinkingBoxContract.Intent.Initialize(messageId))
        println("🔥 [LaunchedEffect] Initialize Intent 已发送完成")
        Timber.tag("TB-LIFECYCLE").d("🔥 [MVI初始化] Initialize Intent 已发送完成")
    }

    val state by viewModel.state.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.effect.collect { effect: ThinkingBoxContract.Effect ->
            when (effect) {
                is ThinkingBoxContract.Effect.NotifyMessageComplete -> {
                    Timber.tag(
                        "ThinkingBox-Lifecycle",
                    ).i(
                        "Data ready for messageId: ${effect.messageId}, finalMarkdown length=${effect.finalMarkdown.length}",
                    )
                    val thinkingData = ThinkingBoxData(
                        messageId = effect.messageId,
                        finalMarkdown = effect.finalMarkdown,
                        thinkingProcess = buildThinkingProcessJson(state),
                        duration = state.thinkingDuration,
                        tokenCount = state.totalTokens,
                    )
                    onDataReady?.invoke(thinkingData)
                }
                else -> { /* Other effects are handled internally */ }
            }
        }
    }

    // The ThinkingBox now self-manages its visibility based on its internal state.
    if (state.isStreaming || state.hasActualThinkingContent || state.isThinkingComplete) {
        ThinkingBoxContent(
            state = state,
            modifier = modifier,
            tokenizerService = tokenizerService,
            onIntent = viewModel::dispatch,
        )
    }
}

/**
 * A helper function to build a JSON string representing the thinking process.
 * This should ideally be moved to a dedicated mapper class.
 */
private fun buildThinkingProcessJson(state: ThinkingBoxContract.State): String {
    // Simple JSON-like string builder to avoid heavy serialization libraries in Composable
    val phases = state.phases.joinToString(",") { phase ->
        """{"id":"${phase.id}","title":"${phase.title?.replace(
            "\"",
            "\\\"",
        )}","content":"${phase.content.replace("\"", "\\\"")}","isComplete":${phase.isComplete}}"""
    }
    return """{"phases":[$phases],"preThinking":"${state.preThinking?.replace("\"", "\\\"") ?: ""}","duration":${state.thinkingDuration},"totalTokens":${state.totalTokens}}"""
}

/**
 * ThinkingBox内容组合器
 * 根据状态决定显示哪些UI组件
 */
@Composable
private fun ThinkingBoxContent(
    state: ThinkingBoxContract.State,
    modifier: Modifier = Modifier,
    tokenizerService: TokenizerService? = null,
    onIntent: (ThinkingBoxContract.Intent) -> Unit,
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
    ) {
        // 1. ThinkingHeader - 显示思考开始状态
        if (state.shouldShowThinkingHeader) {
            com.example.gymbro.features.thinkingbox.internal.presentation.ui.ThinkingHeader(
                title = state.preThinking ?: "thinking...",
                isStreaming = state.isStreaming,
                hasContent = state.hasActualThinkingContent,
                hasPreThinking = !state.preThinking.isNullOrBlank(),
                modifier = Modifier.fillMaxWidth(),
            )
        }

        // 2. AIThinkingCard - 显示思考过程
        if (state.hasActualThinkingContent && !state.shouldShowFinalText) {
            AIThinkingCard(
                uiState = convertToLegacyUiState(state),
                messageId = state.messageId,
                modifier = Modifier.fillMaxWidth(),
                onAnimationFinished = { phaseId ->
                    onIntent(ThinkingBoxContract.Intent.PhaseAnimationFinished(phaseId))
                },
                onSendEvent = { event ->
                    onIntent(ThinkingBoxContract.Intent.HandleThinkingEvent(event))
                },
            )
        }

        // 3. SimpleSummaryText - 显示思考完成摘要
        if (state.isThinkingComplete && !state.shouldShowFinalText) {
            SimpleSummaryTextInternal(
                elapsed = state.thinkingDuration.milliseconds,
                onClick = {
                    onIntent(ThinkingBoxContract.Intent.ToggleSummaryPanel)
                },
                onAnimationComplete = {
                    onIntent(ThinkingBoxContract.Intent.CompleteSummaryAnimation)
                },
                modifier = Modifier.fillMaxWidth(),
            )
        }

        // 4. StreamingFinalRenderer - 显示最终结果
        if (state.shouldShowFinalText) {
            StreamingFinalRenderer(
                finalTokens = state.finalTokens,
                isFinalStreaming = state.isFinalStreaming,
                tokenizerService = tokenizerService,
                onRenderingComplete = {
                    onIntent(ThinkingBoxContract.Intent.CompleteFinalRendering)
                },
                modifier = Modifier.fillMaxWidth(),
            )
        }

        // 5. FinalActionsRow - 显示最终操作按钮
        if (state.shouldShowFinalText && state.isFinalRenderingComplete) {
            val finalContent = state.finalMarkdown ?: state.finalTokens.joinToString("")
            if (finalContent.isNotBlank()) {
                FinalActionsRow(
                    finalContent = finalContent,
                    tokenizerService = tokenizerService,
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }

        // 6. SummaryCard - 显示摘要面板
        if (state.showSummaryPanel) {
            SummaryCard(
                uiState = convertToLegacyUiState(state),
                isExpanded = state.showSummaryPanel,
                onToggle = {
                    onIntent(ThinkingBoxContract.Intent.ToggleSummaryPanel)
                },
                modifier = Modifier.fillMaxWidth(),
            )
        }

        // 7. 错误显示
        state.error?.let { error ->
            ErrorDisplay(
                error = error,
                onDismiss = {
                    onIntent(ThinkingBoxContract.Intent.ClearError)
                },
            )
        }
    }
}

/**
 * 转换为旧版UiState（兼容现有组件）
 */
private fun convertToLegacyUiState(
    state: ThinkingBoxContract.State,
): UiState {
    return UiState(
        phases = state.phases,
        finalMarkdown = state.finalMarkdown,
        isThinking = state.isStreaming,
        activePhaseId = state.activePhaseId,
        isStreaming = state.isStreaming,
        preThinking = state.preThinking,
        finalRichTextReady = state.finalRichTextReady,
        finalContentArrived = state.finalContentArrived,
        isThinkingComplete = state.isThinkingComplete,
        thinkingDuration = state.thinkingDuration,
        totalTokens = state.totalTokens,
    )
}

/**
 * 错误显示组件
 */
@Composable
private fun ErrorDisplay(
    error: UiText,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    // 简单的错误显示实现
    // 可以后续扩展为更复杂的错误UI
}
