下面给出 5 份示意图（均为 **Mermaid**），你可直接放入 Markdown/Docs 渲染。若需导出为 SVG/PNG 或想让我生成可打印版，请告诉我。

---

## 1. 顶层架构示意图（Top-Level Architecture）

```mermaid
flowchart LR
  subgraph Coach Module
    U[User Input]
    C1[CoachPresenter / VM]
    HDB[(History DB)]
    Canvas[Canvas Renderer]
  end

  subgraph Core-Network
    NET[AdaptiveStreamClient<br/>+ StringXmlEscaper]
  end

  subgraph Streaming Infra
    TB[TokenBus]
    TR[TokenRouter]
    CS[ConversationScope]
  end

  subgraph ThinkingBox
    VM[ThinkingBoxViewModel]
    PAR[StreamingThinkingMLParser]
    MAP[DomainMapper]
    GRD[ThinkingMLGuardrail]
    RED[ThinkingReducer]
    ST[Contract.State]
    UI[ThinkingBoxScreen/UI]
    FR[StreamingFinalRenderer]
  end

  %% Flow
  U --> C1 --> NET --> TB --> TR --> CS --> VM
  VM --> PAR --> MAP --> GRD --> RED --> ST --> UI
  ST -->|Effects| C1
  UI -->|PhaseAnimFinished / Close| VM
  MAP --> FR
  FR -->|Final Markdown Ready| RED
  RED -->|NotifyMessageComplete| C1 --> HDB
  C1 --> Canvas
```

---

## 2. 数据链示意图（Data Pipeline）

```mermaid
flowchart TD
  JSON[JSON SSE: {content, reasoning_content}] --> ESC[StringXmlEscaper<br/>(清理/转义非法XML)]
  ESC --> XML[Clean XML Stream]
  XML --> TOK[TokenBus]
  TOK --> ROU[TokenRouter]
  ROU --> SCOPE[ConversationScope.tokens]

  SCOPE --> VMM[ThinkingBoxViewModel]
  VMM --> SYN[StreamingThinkingMLParser<br/>(语法事件)]
  SYN --> SEM[DomainMapper<br/>(语义事件/Phase管理)]
  SEM --> GRD2[ThinkingMLGuardrail<br/>(可选修补)]
  GRD2 --> RED2[ThinkingReducer<br/>(状态机/双时序)]
  RED2 --> STATE[Contract.State]
  STATE --> UI2[ThinkingBox UI]

  SEM --> FINAL[<final> → StreamingFinalRenderer]
  FINAL --> RED2
```

---

## 3. UI 时序示意图（Sequence Diagram）

```mermaid
sequenceDiagram
  participant User
  participant CoachVM as Coach VM
  participant Net as AdaptiveStreamClient
  participant TBVM as ThinkingBox VM
  participant Parser as StreamingThinkingMLParser
  participant Mapper as DomainMapper
  participant Reducer as ThinkingReducer
  participant UI as ThinkingBox UI
  participant FinalR as StreamingFinalRenderer
  participant DB as History DB

  User->>CoachVM: 输入消息
  CoachVM->>Net: 发送至 OpenAI / 获取流(JSON SSE)
  Net->>TBVM: 推送 Clean XML Tokens (via TokenBus/Router/Scope)
  TBVM->>Parser: feed(xmlChunk)
  Parser->>TBVM: SyntaxEvents(OpenTag/Text/CloseTag)
  TBVM->>Mapper: map(SyntaxEvents)
  Mapper->>Reducer: ThinkingEvents(PhaseStart/End, FinalStart 等)
  Reducer-->>UI: Contract.State 更新
  UI->>Reducer: PhaseAnimFinished(X)
  Reducer-->>UI: 允许下一个 Phase 渲染 / 或关闭思考框
  Mapper->>FinalR: FinalTextChunk
  FinalR-->>Reducer: FinalReady
  Reducer-->>CoachVM: NotifyMessageComplete(finalMarkdown)
  CoachVM->>DB: 保存历史
  CoachVM->>UI: 触发 Canvas 渲染最终富文本
```

---

## 4. 状态机示意图（State Machine）

```mermaid
stateDiagram-v2
  [*] --> PERTHINK : StreamStart
  PERTHINK: 预思考阶段
  PERTHINK --> THINKING : <thinking> 或 数据规则切换
  PERTHINK --> FINAL : <final> (数据流启动，但UI仍在perthink)

  state THINKING {
    [*] --> Phase_n
    Phase_n: 当前Phase (id 递增)
    Phase_n --> Phase_n+1 : PhaseEnd(DataDone) && UIAnimDone
    Phase_n+1 --> [*] : </thinking> 收尾
  }

  THINKING --> FINAL : </thinking> 完成 + FinalReady
  FINAL: 最终文本后台渲染阶段
  FINAL --> SHOW_FINAL : 思考框关闭 & FinalReady
  SHOW_FINAL: Final富文本展示
  SHOW_FINAL --> [*]
```

> 说明：
>
> * “数据完成”(DataDone) 来自 Parser/Mapper/Reducer；
> * “UIAnimDone” 来自 UI 的 PhaseAnimFinished 事件；
> * 双时序满足后推进到下一个 Phase 或进入 FINAL。

---

## 5. 文件使用 TREE 示意图（File/Package Tree）

> 示例结构，按你的模块划分；可根据实际 repo 调整。

```mermaid
graph TD
  root[src/]
  root --> core_network
  root --> streaming
  root --> thinkingbox
  root --> coach
  root --> common

  core_network[core-network/]
  core_network --> escaper[StringXmlEscaper.kt]
  core_network --> adaptive[AdaptiveStreamClient.kt]

  streaming[streaming/]
  streaming --> tokenbus[TokenBus.kt]
  streaming --> tokenrouter[TokenRouter.kt]
  streaming --> scope[ConversationScope.kt]

  thinkingbox[thinkingbox/]
  thinkingbox --> vm[ThinkingBoxViewModel.kt]
  thinkingbox --> parser[StreamingThinkingMLParser.kt]
  thinkingbox --> mapper[DomainMapper.kt]
  thinkingbox --> guard[ThinkingMLGuardrail.kt]
  thinkingbox --> reducer[ThinkingReducer.kt]
  thinkingbox --> contract[Contract.kt]
  thinkingbox --> ui[ThinkingBoxScreen.kt]
  thinkingbox --> finalr[StreamingFinalRenderer.kt]

  coach[coach/]
  coach --> presenter[CoachPresenter.kt]
  coach --> history[HistoryRepository.kt]
  coach --> canvas[CanvasRenderer.kt]

  common[common/]
  common --> models[Models.kt]
  common --> utils[XmlStreamScanner.kt]
  common --> logs[LogTags.kt]
```

---

### 下一步？

* 要不要我把这些合并成一个 `finalmermaid大纲.md 2.0`？
* 或生成 PlantUML / Draw\.io / Excalidraw 文件？
* 若你想自动化生成测试样例、或把状态机写成 Kotlin sealed class + reducer 代码模板，我也可以继续补上。

告诉我你想要的格式或后续交付即可。👍
