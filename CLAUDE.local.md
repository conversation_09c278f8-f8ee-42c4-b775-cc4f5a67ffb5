# Kotlin 命名规范

### 1. 包 (Packages)
**规则**: 全部小写，使用 `.` 作为分隔符，禁止使用下划线 `_`。
```kotlin
// ✅
package com.project.feature.data

// ❌
package com.project.Feature.data_source
```

### 2. 文件 (Files)
**规则**: PascalCase，与文件内核心声明的名称一致。
```kotlin
// ✅ 如果文件内核心是 class UserProfile
UserProfile.kt
```

### 3. 类型声明 (Classes, Interfaces, Enums)
**规则**: 使用 PascalCase。
```kotlin
// ✅
class UserProfile
interface NetworkClient
enum class Status
annotation class Inject
object AppConstants
```

### 4. 函数 (Functions)
**规则**: 使用 camelCase，以小写字母开头。
```kotlin
// ✅
fun calculateScore()
fun fetchUserData()

// ❌
fun CalculateScore()
fun fetch_user_data()
```

### 5. 测试函数 (Test Functions)
**规则**: 使用反引号 `` ` `` 包裹自然语言描述的测试场景。
```kotlin
// ✅
@Test
fun `given invalid credentials, login should fail`() { ... }
```

### 6. 属性与变量 (Properties & Variables)
**规则**: 使用 camelCase，以小写字母开头。
- **Backing Property**: 使用下划线 `_` 前缀。
- **Boolean**: 使用 `is`, `has`, `can`, `should` 等作为前缀。
```kotlin
// ✅
val userName: String
val isLoading: Boolean
private val _items = mutableListOf<String>()
val items: List<String> = _items
```

### 7. 常量 (Constants)
**规则**: `const val` 或 `object` 内的 `val` 使用 `SCREAMING_SNAKE_CASE`。
```kotlin
// ✅
const val MAX_RETRIES = 3
object Config {
    const val API_URL = "..."
}
```

### 8. 架构组件 (Architectural Components)
- **UseCase**: `动词 + 名词/名词短语 + UseCase`。
    - ✅ `class GetUserProfileUseCase`
- **Repository**: 接口为 `名词 + Repository`，实现类为 `...Impl`。
    - ✅ `interface UserRepository`
    - ✅ `class UserRepositoryImpl`
- **ViewModel**: `功能模块名 + ViewModel`。
    - ✅ `class UserProfileViewModel`

### 9. Jetpack Compose
- **Composable 函数**: 使用 PascalCase。
    - ✅ `@Composable fun UserProfileCard() { ... }`
- **Preview 函数**:
    1. 必须为 `private`。
    2. 命名为 `被预览组件名 + Preview`。
    3. 必须使用项目级统一的自定义预览注解 (`@GymBroPreview`)。
    ```kotlin
    // ✅
    @GymBroPreview
    @Composable
    private fun UserProfileCardPreview() {
        GymBroTheme {
            UserProfileCard()
        }
    }
    ```
mcp serena
Full List of Tools
Here is the full list of Serena's tools with a short description (output of uv run serena-list-tools):

activate_project: Activates a project by name.
check_onboarding_performed: Checks whether project onboarding was already performed.
create_text_file: Creates/overwrites a file in the project directory.
delete_lines: Deletes a range of lines within a file.
delete_memory: Deletes a memory from Serena's project-specific memory store.
execute_shell_command: Executes a shell command.
find_referencing_code_snippets: Finds code snippets in which the symbol at the given location is referenced.
find_referencing_symbols: Finds symbols that reference the symbol at the given location (optionally filtered by type).
find_symbol: Performs a global (or local) search for symbols with/containing a given name/substring (optionally filtered by type).
get_active_project: Gets the name of the currently active project (if any) and lists existing projects
get_current_config: Prints the current configuration of the agent, including the active modes, tools, and context.
get_symbols_overview: Gets an overview of the top-level symbols defined in a given file or directory.
initial_instructions: Gets the initial instructions for the current project. Should only be used in settings where the system prompt cannot be set, e.g. in clients you have no control over, like Claude Desktop.
insert_after_symbol: Inserts content after the end of the definition of a given symbol.
insert_at_line: Inserts content at a given line in a file.
insert_before_symbol: Inserts content before the beginning of the definition of a given symbol.
list_dir: Lists files and directories in the given directory (optionally with recursion).
list_memories: Lists memories in Serena's project-specific memory store.
onboarding: Performs onboarding (identifying the project structure and essential tasks, e.g. for testing or building).
prepare_for_new_conversation: Provides instructions for preparing for a new conversation (in order to continue with the necessary context).
read_file: Reads a file within the project directory.
read_memory: Reads the memory with the given name from Serena's project-specific memory store.
replace_lines: Replaces a range of lines within a file with new content.
replace_symbol_body: Replaces the full definition of a symbol.
restart_language_server: Restarts the language server, may be necessary when edits not through Serena happen.
search_for_pattern: Performs a search for a pattern in the project.
summarize_changes: Provides instructions for summarizing the changes made to the codebase.
switch_modes: Activates modes by providing a list of their names
think_about_collected_information: Thinking tool for pondering the completeness of collected information.
think_about_task_adherence: Thinking tool for determining whether the agent is still on track with the current task.
think_about_whether_you_are_done: Thinking tool for determining whether the task is truly completed.

