package com.example.gymbro.core.network.security

import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * StringXmlEscaper - XML 字符转义和内容清理工具
 *
 * 🎯 核心职责：
 * - 转义 XML 特殊字符，确保内容可以安全嵌入 XML 标签中
 * - 清理非法的 phase 标签（如 <phase:PLAN>），保留文本内容
 * - 标准化 token 格式，确保输出到 TokenBus 的都是安全的 XML
 * - 处理 JSON SSE 格式到标准 XML 格式的转换
 *
 * 🔥 设计原则：
 * - 输入验证在边界：在 core-network 层确保输出安全
 * - 单一职责：专注于 XML 安全处理，不涉及业务逻辑
 * - 性能优化：使用高效的正则表达式和字符串操作
 * - 错误容错：确保即使输入异常也能产生安全的输出
 */
@Singleton
class StringXmlEscaper @Inject constructor() {

    companion object {
        private const val TAG = "StringXmlEscaper"

        // 🔥 XML 特殊字符映射
        private val XML_ESCAPE_MAP = mapOf(
            "&" to "&amp;",
            "<" to "&lt;",
            ">" to "&gt;",
            "\"" to "&quot;",
            "'" to "&#39;",
        )

        // 🔥 非法 phase 标签正则表达式
        // 匹配 <phase:XXX>文本内容</phase:XXX> 并提取文本内容
        private val PHASE_TAG_WITH_CONTENT_REGEX =
            Regex("<phase:([A-Z_]+)>(.*?)</phase:\\1>", RegexOption.DOT_MATCHES_ALL)

        // 🔥 匹配单独的开始和结束标签
        private val PHASE_START_TAG_REGEX = Regex("<phase:[A-Z_]+>")
        private val PHASE_END_TAG_REGEX = Regex("</phase:[A-Z_]+>")

        // 🔥 匹配不完整的 phase 标签（在 token 边界被截断）
        private val INCOMPLETE_PHASE_START_REGEX = Regex("<phase:[A-Z_]*$")
        private val INCOMPLETE_PHASE_END_REGEX = Regex("^[A-Z_]*>")
        private val INCOMPLETE_PHASE_CLOSE_REGEX = Regex("</phase:[A-Z_]*$")

        // 🔥 其他可能的非法标签模式
        private val OTHER_ILLEGAL_PATTERNS = listOf(
            Regex("<[a-zA-Z]+:[a-zA-Z_]+>"), // 匹配 <any:any> 格式
            // 注意：不包括合法的 XML 命名空间，如 <phase id="1">
        )
    }

    /**
     * 🔥 【核心方法】转义 XML 特殊字符
     *
     * 确保字符串内容可以安全地嵌入到 XML 标签中，防止 XML 注入攻击
     *
     * @param content 原始内容
     * @return 转义后的安全内容
     */
    fun escapeXmlContent(content: String): String {
        if (content.isEmpty()) return content

        return try {
            var escaped = content
            XML_ESCAPE_MAP.forEach { (char, escape) ->
                escaped = escaped.replace(char, escape)
            }

            Timber.tag(TAG).v("XML转义完成: 原始长度=${content.length}, 转义后长度=${escaped.length}")
            escaped
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "XML转义失败，返回空字符串")
            ""
        }
    }

    /**
     * 🔥 【核心方法】清理非法的 phase 标签，保留文本内容
     *
     * 处理 AI 输出中的非标准标签，如：
     * <phase:PLAN>我们正在分析</phase:PLAN> → 我们正在分析
     *
     * @param content 包含非法标签的内容
     * @return 清理后的纯文本内容
     */
    fun cleanPhaseTagsToText(content: String): String {
        if (content.isEmpty()) return content

        return try {
            var cleaned = content

            // 🔥 第一步：处理完整的 <phase:XXX>文本</phase:XXX>，保留文本内容
            PHASE_TAG_WITH_CONTENT_REGEX.findAll(cleaned).forEach { match ->
                val fullMatch = match.value
                val textContent = match.groupValues[2] // 提取文本内容
                Timber.tag(TAG).d("提取phase标签内容: '$fullMatch' → '$textContent'")
                cleaned = cleaned.replace(fullMatch, textContent)
            }

            // 🔥 第二步：删除单独的开始和结束标签
            if (PHASE_START_TAG_REGEX.containsMatchIn(cleaned)) {
                Timber.tag(TAG).d("删除phase开始标签")
                cleaned = cleaned.replace(PHASE_START_TAG_REGEX, "")
            }

            if (PHASE_END_TAG_REGEX.containsMatchIn(cleaned)) {
                Timber.tag(TAG).d("删除phase结束标签")
                cleaned = cleaned.replace(PHASE_END_TAG_REGEX, "")
            }

            // 🔥 第三步：处理不完整的标签（在token边界被截断）
            cleaned = cleaned.replace(INCOMPLETE_PHASE_START_REGEX, "")
            cleaned = cleaned.replace(INCOMPLETE_PHASE_END_REGEX, "")
            cleaned = cleaned.replace(INCOMPLETE_PHASE_CLOSE_REGEX, "")

            // 🔥 第四步：处理其他非法标签模式
            OTHER_ILLEGAL_PATTERNS.forEach { pattern ->
                if (pattern.containsMatchIn(cleaned)) {
                    Timber.tag(TAG).d("清洗其他非法标签: ${pattern.pattern}")
                    cleaned = cleaned.replace(pattern, "")
                }
            }

            // 🔥 验证清理效果
            if (content.length != cleaned.length) {
                Timber.tag(TAG).i("标签清理完成: 原始=${content.length}字符, 清理后=${cleaned.length}字符")
            }

            cleaned
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "标签清理失败，返回原始内容")
            content
        }
    }

    /**
     * 🔥 【组合方法】完整的内容清理和转义
     *
     * 结合标签清理和 XML 转义，提供一站式的内容安全处理
     *
     * @param content 原始内容
     * @return 清理并转义后的安全内容
     */
    fun sanitizeAndEscape(content: String): String {
        if (content.isEmpty()) return content

        return try {
            // 先清理非法标签，再进行 XML 转义
            val cleaned = cleanPhaseTagsToText(content)
            val escaped = escapeXmlContent(cleaned)

            Timber.tag(TAG).v("完整清理完成: 原始=${content.length}字符, 最终=${escaped.length}字符")
            escaped
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "完整清理失败，返回空字符串")
            ""
        }
    }

    /**
     * 🔥 【验证方法】检查内容是否包含非法标签
     *
     * @param content 待检查的内容
     * @return true 如果包含非法标签
     */
    fun hasIllegalTags(content: String): Boolean {
        if (content.isEmpty()) return false

        return try {
            PHASE_TAG_WITH_CONTENT_REGEX.containsMatchIn(content) ||
                PHASE_START_TAG_REGEX.containsMatchIn(content) ||
                PHASE_END_TAG_REGEX.containsMatchIn(content) ||
                OTHER_ILLEGAL_PATTERNS.any { it.containsMatchIn(content) }
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "非法标签检查失败")
            false
        }
    }

    /**
     * 🔥 【统计方法】获取清理统计信息
     *
     * @param originalContent 原始内容
     * @param cleanedContent 清理后的内容
     * @return 清理统计信息
     */
    fun getCleaningStats(originalContent: String, cleanedContent: String): CleaningStats {
        return try {
            val removedChars = originalContent.length - cleanedContent.length
            val illegalTagCount = PHASE_TAG_WITH_CONTENT_REGEX.findAll(originalContent).count() +
                PHASE_START_TAG_REGEX.findAll(originalContent).count() +
                PHASE_END_TAG_REGEX.findAll(originalContent).count() +
                OTHER_ILLEGAL_PATTERNS.sumOf { pattern ->
                    pattern.findAll(originalContent).count()
                }

            CleaningStats(
                originalLength = originalContent.length,
                cleanedLength = cleanedContent.length,
                removedChars = removedChars,
                illegalTagCount = illegalTagCount,
            )
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "统计信息计算失败")
            CleaningStats(
                originalLength = originalContent.length,
                cleanedLength = cleanedContent.length,
                removedChars = 0,
                illegalTagCount = 0,
            )
        }
    }

    /**
     * 🔥 【轻量级方法】仅进行基础字符清理
     *
     * 用于高频调用场景，只做最基本的字符修复
     *
     * @param token 原始 token
     * @return 清理后的 token
     */
    fun sanitizeToken(token: String): String {
        if (token.isEmpty()) return token

        return try {
            token
                .replace("\u0000", "") // 移除null字符
                .replace("\uFFFD", "") // 移除替换字符
                .replace(Regex("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]"), "") // 移除控制字符
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "Token清理失败")
            token
        }
    }
}

/**
 * 清理统计信息数据类
 */
data class CleaningStats(
    val originalLength: Int,
    val cleanedLength: Int,
    val removedChars: Int,
    val illegalTagCount: Int,
) {
    /**
     * 清理效率（移除字符占原始内容的比例）
     */
    val cleaningEfficiency: Float = if (originalLength > 0) {
        removedChars.toFloat() / originalLength.toFloat()
    } else {
        0f
    }

    /**
     * 是否进行了有效清理
     */
    val hasEffectiveCleaning: Boolean = removedChars > 0 || illegalTagCount > 0
}
