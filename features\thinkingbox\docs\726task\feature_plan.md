# ThinkingBox 模块架构重构规划方案

## 概述

基于 `726thinkingbox修复方案.md` 和 `726顶层mermaid示意图.md` 技术文档，本方案将重构 ThinkingBox 模块为完整的 MVI 2.0 架构，遵循 Clean Architecture 原则，实现流式 AI 思考过程的可视化。

## 架构目标

1. **单一数据流管道**：AdaptiveStreamClient → StringXmlEscaper → TokenBus → TokenRouter → ConversationScope → ThinkingBoxViewModel
2. **双时序架构**：数据处理时序与 UI 动画时序分离，通过 PhaseAnimFinished 事件协调
3. **组件职责明确**：每个组件单一职责，避免重复处理和职责重叠
4. **状态机管理**：PERTHINK → THINKING → FINAL 三阶段状态管理
5. **完整 MVI 实现**：Contract、Reducer、EffectHandler、ViewModel 标准结构

## 核心架构组件规划

### 1. 数据流管道 (Token Pipeline)

#### 1.1 边界处理 - core-network 层
- **StringXmlEscaper**: 在 AdaptiveStreamClient 中完成所有 XML 清理工作
  - 清理非法标签 (`<phase:PLAN>` → 纯文本)
  - 注入 `<think>` 和 `</think>` 包装
  - 确保输出安全的 XML 流

#### 1.2 Token 路由 - 流基础设施
```mermaid
flowchart LR
  AdaptiveStreamClient --> TokenBus
  TokenBus --> TokenRouter  
  TokenRouter --> ConversationScope
  ConversationScope --> ThinkingBoxViewModel
```

- **TokenBus**: 中心化 token 事件总线
- **TokenRouter**: 按 messageId 路由到对应会话范围
- **ConversationScope**: 会话级 token 流管理

### 2. ThinkingBox 核心处理链

#### 2.1 数据处理流
```kotlin
// 主要数据流：5个核心组件
ConversationScope.tokens 
  → StreamingThinkingMLParser (语法事件)
  → DomainMapper (语义事件 → ThinkingEvent)  
  → ThinkingMLGuardrail (可选修复)
  → ThinkingReducer (状态机 + 双时序)
  → Contract.State
```

#### 2.2 组件职责分配

**StreamingThinkingMLParser**
- 职责：纯 XML 语法解析器
- 输入：XML token 流
- 输出：SemanticEvent (TagOpened, TagClosed, TextChunk)
- 状态：PRE_THINK → THINKING → POST_FINAL
- 约束：不处理业务逻辑，不维护 phase 状态

**DomainMapper** 
- 职责：语义事件到业务事件映射
- 输入：SemanticEvent
- 输出：ThinkingEvent (PhaseStart, PhaseContent, PhaseEnd, FinalStart 等)
- 核心功能：
  - Phase ID 分配和管理
  - Title 缓冲和解析
  - 业务状态转换 (perthink → thinking → final)

**ThinkingMLGuardrail** (可选)
- 职责：语义级修复和验证
- 功能：补充缺失的 `</phase>` 标签、修复乱序标签
- 位置：DomainMapper 之后，Reducer 之前

**ThinkingReducer**
- 职责：状态机 + 双时序协调
- 核心功能：
  - 维护 phases: LinkedHashMap<String, PhaseUi>
  - 管理 pending: ArrayDeque<String> 等待队列  
  - 双时序握手：数据完成 + UI 动画完成
  - 状态转换：PERTHINK → THINKING → FINAL

### 3. MVI Contract 设计

#### 3.1 Contract.State 结构
```kotlin
@Immutable
data class State(
    // 核心状态
    val messageId: String = "",
    val phases: List<PhaseUi> = emptyList(),       // UI 消费的 phase 列表
    val activePhaseId: String? = null,             // 当前活跃 phase
    val preThinking: String? = null,               // perthink 文本内容
    
    // 流式状态
    val isStreaming: Boolean = false,
    val isThinkingComplete: Boolean = false,
    
    // Final 相关
    val finalMarkdown: String? = null,             // 最终富文本内容
    val finalTokens: List<String> = emptyList(),   // Final token 列表 (TypewriterRenderer)
    val finalRichTextReady: Boolean = false,       // StreamingFinalRenderer 就绪
    val finalContentArrived: Boolean = false,      // Final 数据已到达
    val isFinalStreaming: Boolean = false,         // Final 流式状态
    
    // UI 控制
    val summaryTextVisible: Boolean = false,
    val showSummaryPanel: Boolean = false,
    val isFinalRenderingComplete: Boolean = false,
    
    // 计算属性
    val hasActualThinkingContent: Boolean,         // 是否有实际思考内容
    val shouldShowFinalText: Boolean,              // 是否显示 final 文本
    val shouldShowThinkingHeader: Boolean          // 是否显示思考头部
)
```

#### 3.2 Intent 设计
```kotlin
sealed interface Intent {
    // 生命周期
    data class Initialize(val messageId: String) : Intent
    data object Reset : Intent
    
    // 事件处理 
    data class HandleThinkingEvent(val event: ThinkingEvent) : Intent
    
    // UI 交互 (双时序关键)
    data class PhaseAnimationFinished(val phaseId: String) : Intent
    data object ToggleSummaryPanel : Intent
    data object CompleteFinalRendering : Intent
    
    // 错误处理
    data object ClearError : Intent
}
```

#### 3.3 Effect 设计
```kotlin
sealed interface Effect {    
    // 外部通信
    data class NotifyMessageComplete(
        val messageId: String, 
        val finalMarkdown: String
    ) : Effect
    
    // UI 控制
    data object ScrollToBottom : Effect
    
    // 错误处理
    data class ShowError(val error: UiText) : Effect
    
    // 调试日志
    data class LogDebug(val message: String) : Effect
}
```

### 4. 状态机设计

#### 4.1 三阶段状态转换
```mermaid
stateDiagram-v2
    [*] --> PERTHINK : StreamStart
    PERTHINK --> THINKING : <thinking> 检测
    THINKING --> Phase_1 : <phase id="1">
    Phase_1 --> Phase_2 : PhaseEnd + PhaseAnimFinished  
    Phase_2 --> Phase_n : 循环直到 </thinking>
    THINKING --> FINAL : </thinking> + <final>
    FINAL --> [*] : 思考框关闭 + FinalReady
```

#### 4.2 双时序握手机制
```kotlin
// 数据时序：Parser/Mapper 驱动
ThinkingEvent.PhaseEnd(phaseId) → phase.isComplete = true

// UI 时序：UI 动画完成驱动  
ThinkingEvent.PhaseAnimFinished(phaseId) → 检查双时序条件

// 双时序条件：数据完成 && UI 动画完成 && 是当前活跃 phase
if (phase.isComplete && activePhaseId == phaseId) {
    advanceToNextPhase() // 切换到下一个 phase 或完成思考
}
```

### 5. UI 层设计

#### 5.1 主要 Composable 组件
- **ThinkingBoxScreen**: 主屏幕容器，状态收集和事件分发
- **ThinkingHeader**: 显示 preThinking 内容
- **ThinkingStageCard**: 单个 phase 的卡片展示  
- **StreamingFinalRenderer**: 最终富文本打字机效果
- **AnimationEngine**: 统一动画管理和时序协调

#### 5.2 UI 事件流
```kotlin
// UI → ViewModel 事件流
UI.onPhaseAnimationComplete() 
  → viewModel.sendIntent(PhaseAnimationFinished(phaseId))
  → reducer.reduce(state, PhaseAnimFinished(phaseId))
  → 检查双时序条件并推进状态
```

### 6. 与 Coach 模块集成

#### 6.1 接口定义
```kotlin
// Coach 模块调用接口
@Composable
fun ThinkingBox(
    messageId: String,                          // 消息ID  
    onDataReady: (String, String) -> Unit       // (messageId, finalMarkdown) → 保存历史
)

// 内部 Effect 处理
viewModelScope.launch {
    effects.collect { effect ->
        when (effect) {
            is Effect.NotifyMessageComplete -> {
                onDataReady(effect.messageId, effect.finalMarkdown)
            }
        }
    }
}
```

#### 6.2 防重复调用机制
```kotlin
// Coach 端防重复保存
private val processedMessages = mutableSetOf<String>()

val onDataReady = { messageId: String, finalMarkdown: String ->
    if (messageId !in processedMessages) {
        processedMessages.add(messageId)
        // 保存历史记录
        saveToHistory(messageId, finalMarkdown)
    }
}
```

### 7. 实现计划

#### 7.1 Phase 1: 核心管道重构
1. **验证 TokenRouter 流**：确保 ConversationScope 正确接收 tokens
2. **重构 StreamingThinkingMLParser**：纯语法解析器实现
3. **优化 DomainMapper**：完善事件映射逻辑
4. **测试数据流完整性**：端到端 token → SemanticEvent → ThinkingEvent

#### 7.2 Phase 2: Reducer 状态机完善  
1. **实现双时序握手**：PhaseAnimFinished 事件处理
2. **完善状态转换**：PERTHINK → THINKING → FINAL
3. **优化队列管理**：pending phases 的正确处理
4. **状态验证**：validateState 方法和调试工具

#### 7.3 Phase 3: UI 层优化
1. **Contract 状态映射**：Reducer state → Contract state 转换
2. **动画时序协调**：AnimationEngine 和事件回调
3. **StreamingFinalRenderer 集成**：最终富文本渲染
4. **性能优化**：reduce 重组频率和内存使用

#### 7.4 Phase 4: 集成测试
1. **端到端测试**：完整的 AI 对话流程
2. **边界情况处理**：恶意输入、网络中断、解析错误  
3. **性能基准**：内存使用、渲染延迟、电池消耗
4. **多设备兼容性**：不同屏幕尺寸和系统版本

### 8. 质量保证

#### 8.1 测试策略
- **单元测试**: Reducer 逻辑、DomainMapper 映射、Parser 解析
- **集成测试**: ViewModel + Reducer + UI 状态同步  
- **端到端测试**: 完整的 Coach → ThinkingBox → 历史记录流程
- **性能测试**: 大量 token 流处理、内存泄漏检测

#### 8.2 错误处理
- **解析错误**: 恶意 XML、不完整标签的降级处理
- **网络错误**: Token 流中断、重连机制
- **状态错误**: 无效状态转换的恢复策略  
- **内存错误**: OOM 保护和状态回收

#### 8.3 监控和调试
- **结构化日志**: TB-PARSER, TB-MAPPER, TB-REDUCER 标签系统
- **性能监控**: Token 处理延迟、UI 帧率、内存使用  
- **状态快照**: 关键状态转换的序列化和恢复
- **A/B 测试**: 不同算法和 UI 方案的效果对比

## 总结

本架构方案通过以下核心改进实现完整的 ThinkingBox 重构：

1. **清晰的数据管道**: 从 AdaptiveStreamClient 到 UI 的单向数据流
2. **明确的组件职责**: 每个组件单一职责，避免重复处理  
3. **标准 MVI 架构**: Contract、Reducer、EffectHandler 完整实现
4. **双时序协调**: 数据处理与 UI 动画的分离和同步
5. **完整的错误处理**: 从解析错误到网络中断的全覆盖
6. **性能优化**: 内存使用、渲染效率、电池消耗的全面优化

该方案完全符合 GymBro 项目的 Clean Architecture + MVI 2.0 标准，为 AI 思考过程可视化提供稳定、高效、可维护的技术基础。