package com.example.gymbro.core.logging

import android.annotation.SuppressLint
import android.util.Log
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【重构】增强的 Timber 日志管理器
 *
 * 统一管理Timber日志系统的初始化、配置和控制。
 * 集成模块级别的日志控制和性能优化。
 */
@Singleton
class TimberManager
@Inject
constructor(
    private val loggingConfig: LoggingConfig,
) {
    /**
     * 初始化日志系统
     *
     * @param isDebug 是否为调试模式
     * @param environment 环境类型
     * @param enableThinkingBoxMode 是否启用ThinkingBox专用模式
     */
    fun initialize(
        isDebug: Boolean,
        environment: LoggingConfig.Environment = LoggingConfig.Environment.DEVELOPMENT,
        enableThinkingBoxMode: Boolean = true, // 🔥 【临时调试】启用ThinkingBox专用日志
    ) {
        // 设置环境
        loggingConfig.setEnvironment(environment)

        // 清除所有已有的Tree
        Timber.uprootAll()

        // 根据环境和模式安装适当的Tree
        when {
            isDebug && enableThinkingBoxMode -> {
                // ThinkingBox专用模式：使用ThinkingBoxAwareTree + WorkoutLogTree
                Timber.plant(ThinkingBoxAwareTree(loggingConfig))
                // 动态加载WorkoutLogTree
                try {
                    val workoutLogTreeClass = Class.forName(
                        "com.example.gymbro.features.workout.logging.WorkoutLogTree",
                    )
                    val constructor = workoutLogTreeClass.getConstructor(LoggingConfig::class.java)
                    val workoutLogTree = constructor.newInstance(loggingConfig) as Timber.Tree
                    Timber.plant(workoutLogTree)
                    Timber.tag("LOG-MANAGER").i("🔥 WorkoutLogTree 已加载")
                } catch (e: Exception) {
                    Timber.tag("LOG-MANAGER").w("WorkoutLogTree 加载失败: ${e.message}")
                }
                Timber.tag("LOG-MANAGER").i("🔥 ThinkingBox专用日志系统已启动 - 深度调试模式 + WK支持")
            }

            isDebug -> {
                // 开发环境：使用模块感知的Tree + WorkoutLogTree
                Timber.plant(ModuleAwareTree(loggingConfig))
                // 动态加载WorkoutLogTree
                try {
                    val workoutLogTreeClass = Class.forName(
                        "com.example.gymbro.features.workout.logging.WorkoutLogTree",
                    )
                    val constructor = workoutLogTreeClass.getConstructor(LoggingConfig::class.java)
                    val workoutLogTree = constructor.newInstance(loggingConfig) as Timber.Tree
                    Timber.plant(workoutLogTree)
                    Timber.tag("LOG-MANAGER").i("🔥 WorkoutLogTree 已加载")
                } catch (e: Exception) {
                    Timber.tag("LOG-MANAGER").w("WorkoutLogTree 加载失败: ${e.message}")
                }
                Timber.tag("LOG-MANAGER").i("🔥 开发环境日志系统已启动 - 模块感知模式 + WK支持")
            }

            environment == LoggingConfig.Environment.PRODUCTION -> {
                // 生产环境：只记录错误
                Timber.plant(ReleaseTree())
                Timber.tag("LOG-MANAGER").i("🔥 生产环境日志系统已启动 - 仅错误模式")
            }

            else -> {
                // 测试环境：适度日志
                Timber.plant(ModuleAwareTree(loggingConfig))
                Timber.tag("LOG-MANAGER").i("🔥 测试环境日志系统已启动 - 适度日志模式")
            }
        }

        // 启用协程调试支持（仅在调试模式下）
        if (isDebug) {
            System.setProperty("kotlinx.coroutines.debug", "on")
        }
    }

    /**
     * 🔥 【新增】专用于应用层的ThinkingBox模式初始化
     *
     * 符合Clean Architecture原则，应用层通过此方法启用ThinkingBox功能
     * 而无需直接依赖features模块
     */
    fun initializeWithThinkingBoxSupport(isDebug: Boolean) {
        initialize(
            isDebug = isDebug,
            environment = if (isDebug) LoggingConfig.Environment.DEVELOPMENT else LoggingConfig.Environment.PRODUCTION,
            enableThinkingBoxMode = isDebug,
        )

        if (isDebug) {
            // 配置ThinkingBox专用的模块设置
            enableThinkingBoxDebugLogs()
            // 🔥 移除噪音日志：ThinkingBox专用模式启动信息
        }
    }

    /**
     * 启用ThinkingBox调试日志
     */
    fun enableThinkingBoxDebugLogs() {
        loggingConfig.updateModuleConfig(
            LoggingConfig.MODULE_THINKING_BOX,
            LoggingConfig.ModuleLogConfig(
                enabled = true,
                minLevel = Log.DEBUG,
                tags = setOf(
                    "TB-ERROR", "TB-STATE", "TB-UI", "TB-CONTENT",
                    "TB-RAW-COLLECTOR", // 更新后的标签
                    // 🔥 【新增RAW TOKEN采集标签】用于调试XML解析问题
                    "TB-RAW-TOKEN", "TB-XML-TOKEN", "TB-TAG-THINK", "TB-TAG-THINKING",
                    "TB-TAG-PHASE", "TB-TAG-FINAL", "TB-TAG-CLOSE", "TB-PROCESSED",
                    "TB-XML-INPUT", "TB-XML-BUFFER", "TB-XML-PARTIAL", "TB-XML-PARSE", "TB-XML-OUTPUT",
                ),
                sampleRate = 1,
            ),
        )
        Timber.tag("LOG-MANAGER").i("🔥 ThinkingBox 调试日志已启用")
    }

    /**
     * 运行时切换环境
     */
    fun switchEnvironment(environment: LoggingConfig.Environment) {
        loggingConfig.setEnvironment(environment)
        Timber.tag("LOG-MANAGER").i("🔥 已切换到环境: $environment")
    }

    /**
     * 获取当前环境
     */
    fun getCurrentEnvironment(): LoggingConfig.Environment = loggingConfig.getCurrentEnvironment()

    /**
     * 设置全局日志标签过滤器
     *
     * @param tagFilter 标签过滤函数
     */
    fun setGlobalTagFilter(tagFilter: (String?) -> String) {
        globalTagFilter = tagFilter
    }

    companion object {
        private const val LOG_STACK_INDICATOR = "LogStack"

        // 全局标签过滤器
        @Volatile
        private var globalTagFilter: ((String?) -> String)? = null

        /**
         * 应用全局标签过滤器
         */
        internal fun applyTagFilter(
            tag: String?,
        ): String = globalTagFilter?.invoke(tag) ?: tag ?: "GymBro"

        /**
         * 基于当前类名生成的标签
         */
        @SuppressLint("DefaultLocale")
        fun createTag(): String {
            val stackTrace = Thread.currentThread().stackTrace
            for (element in stackTrace) {
                val className = element.className
                if (!className.contains("TimberManager") &&
                    !className.contains("Thread") &&
                    !className.contains("VMStack") &&
                    !className.contains("Method") &&
                    !className.startsWith("java.") &&
                    !className.startsWith("android.") &&
                    !className.startsWith("dalvik.")
                ) {
                    val fullClassName = className.substringAfterLast('.')
                    return fullClassName.substring(fullClassName.lastIndexOf('.') + 1)
                }
            }
            return "Unknown"
        }
    }
}
