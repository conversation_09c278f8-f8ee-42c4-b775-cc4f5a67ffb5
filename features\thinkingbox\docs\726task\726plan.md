请根据以下两个文档中的技术方案，重构并完善 ThinkingBox 模块的完整架构：

**参考文档：**
- `features\thinkingbox\docs\726thinkingbox修复方案.md`
- `features\thinkingbox\docs\726顶层mermaid示意图.md`

**交付要求：**
1. **完整架构实现**：基于文档方案，提供 ThinkingBox 模块的完整代码实现
2. **清晰职责分离**：每个文件/类必须有明确的单一职责，遵循 Clean Architecture 原则
3. **MVI 架构规范**：严格按照项目的 MVI 2.0 标准实现（Contract、Reducer、EffectHandler、ViewModel）
4. **文件结构说明**：为每个新建或修改的文件提供清晰的职责说明和在整体架构中的作用

**具体实施步骤：**
1. 首先阅读并分析两个文档中的技术方案
2. 基于现有 ThinkingBox 代码结构，按照文档方案进行重构
3. 确保新架构符合项目的依赖注入（Hilt）和错误处理（Result<T>）规范
4. 提供完整的、可编译的代码文件，无占位符或 TODO
5. 说明每个组件在 ThinkingBox 架构中的具体职责和交互关系

**质量标准：**
- 代码必须基于现有代码库的实际结构
- 遵循项目的 Kotlin 命名规范和代码风格
- 确保架构的可测试性和可维护性
