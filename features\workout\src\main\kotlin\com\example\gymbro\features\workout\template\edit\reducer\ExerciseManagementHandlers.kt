package com.example.gymbro.features.workout.template.edit.reducer

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.features.workout.logging.WorkoutLogTree
import com.example.gymbro.features.workout.template.TemplateContract
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import timber.log.Timber
import javax.inject.Inject

/**
 * 动作管理处理器
 *
 * 🎯 职责：
 * - 处理动作的增删改查操作
 * - 管理动作数据的状态转换
 * - 确保数据一致性
 *
 * 📋 遵循标准：
 * - 单一职责原则
 * - 纯函数式编程
 * - 不可变状态管理
 */
class ExerciseManagementHandlers @Inject constructor() {

    // === 动作添加 ===

    fun handleAddExercise(
        intent: TemplateEditContract.Intent.AddExercise,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        Timber.d("🔧 [AddExercise] 开始添加动作: ${intent.exercise.name}")
        Timber.d("🔧 [AddExercise] 当前动作数量: ${state.exercises.size}")
        Timber.d("🔧 [AddExercise] 最大限制: ${TemplateEditContract.Constants.MAX_EXERCISES_PER_TEMPLATE}")

        // 检查动作数量限制
        if (state.exercises.size >= TemplateEditContract.Constants.MAX_EXERCISES_PER_TEMPLATE) {
            Timber.w(
                "🔧 [AddExercise] 达到动作数量限制: ${state.exercises.size}/${TemplateEditContract.Constants.MAX_EXERCISES_PER_TEMPLATE}",
            )
            return ReduceResult.withEffect(
                state,
                TemplateEditContract.Effect.ShowToast(
                    UiText.DynamicString(
                        "最多只能添加 ${TemplateEditContract.Constants.MAX_EXERCISES_PER_TEMPLATE} 个动作",
                    ),
                ),
            )
        }

        val newExercise = mapExerciseToDto(intent.exercise)
        val updatedExercises = state.exercises + newExercise

        // 调试日志
        Timber.d("🔧 [AddExercise] 新动作ID: ${newExercise.id}")
        Timber.d("🔧 [AddExercise] 新动作 customSets: ${newExercise.customSets.size}")
        newExercise.customSets.forEachIndexed { index, set ->
            Timber.d(
                "🔧 [AddExercise] 新动作组${index + 1}: weight=${set.targetWeight}, reps=${set.targetReps}, rest=${set.restTimeSeconds}s",
            )
        }

        return ReduceResult.stateOnly(
            state.copy(
                exercises = updatedExercises,
                hasUnsavedChanges = true,
                // 🔥 修复：不再触发自动保存，避免转圈圈问题
            ),
        )
    }

    fun handleAddExercises(
        intent: TemplateEditContract.Intent.AddExercises,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        val newExercises = intent.exercises.map { exercise ->
            mapExerciseToDto(exercise)
        }

        // 检查批量添加后的动作数量限制
        val totalExercisesAfterAdd = state.exercises.size + newExercises.size
        if (totalExercisesAfterAdd > TemplateEditContract.Constants.MAX_EXERCISES_PER_TEMPLATE) {
            val maxCanAdd = TemplateEditContract.Constants.MAX_EXERCISES_PER_TEMPLATE - state.exercises.size
            return ReduceResult.withEffect(
                state,
                TemplateEditContract.Effect.ShowToast(
                    UiText.DynamicString(
                        "最多只能再添加 $maxCanAdd 个动作（当前 ${state.exercises.size}/${TemplateEditContract.Constants.MAX_EXERCISES_PER_TEMPLATE}）",
                    ),
                ),
            )
        }

        // 调试日志
        Timber.d("🔧 [AddExercises] 批量添加 ${newExercises.size} 个动作")
        newExercises.forEachIndexed { index, exercise ->
            Timber.d(
                "🔧 [AddExercises] 动作${index + 1}: ${exercise.exerciseName}, customSets=${exercise.customSets.size}",
            )
        }

        return ReduceResult.stateOnly(
            state.copy(
                exercises = state.exercises + newExercises,
                hasUnsavedChanges = true,
                // 🔥 修复：不再触发自动保存，避免转圈圈问题
            ),
        )
    }

    // === 动作更新 ===

    fun handleUpdateExercise(
        intent: TemplateEditContract.Intent.UpdateExercise,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        // 🔥 WK跟踪：动作更新触发
        Timber.d("🔥 [WK-UPDATE-EXERCISE] 动作更新被触发: ${intent.exercise.exerciseName}")
        Timber.d("🔥 [WK-UPDATE-EXERCISE] customSets数量: ${intent.exercise.customSets.size}")

        // 🔥 保留关键调试日志用于数据验证
        WorkoutLogTree.Exercise.debug(
            "🔥 handleUpdateExercise 被调用: 动作=${intent.exercise.exerciseName}, customSets=${intent.exercise.customSets.size}",
        )

        intent.exercise.customSets.forEachIndexed { index, set ->
            WorkoutLogTree.Exercise.debug(
                "🔥 Intent组${index + 1}: weight=${set.targetWeight}, reps=${set.targetReps}, rest=${set.restTimeSeconds}s",
            )
        }

        val updatedExercises = state.exercises.map { exercise ->
            if (exercise.id == intent.exercise.id) {
                // 🔥 关键修复：直接使用更新后的动作数据，不调用 syncExerciseData
                val updatedExercise = intent.exercise
                updatedExercise
            } else {
                exercise
            }
        }

        // 🔥 新增：验证状态更新后的数据完整性
        val updatedState = state.copy(
            exercises = updatedExercises,
            hasUnsavedChanges = true,
            autoSaveState = TemplateContract.AutoSaveState.Inactive,
        )

        // 🔥 WK跟踪：即将触发自动保存Effect
        Timber.d("🔥 [WK-UPDATE-EXERCISE] 即将触发TriggerAutoSave Effect")
        Timber.d("🔥 [WK-UPDATE-EXERCISE] 注意: 自动保存功能已被禁用，只会记录日志")

        return ReduceResult.stateOnly(
            updatedState,
            // 🔥 修复：不再触发自动保存，避免转圈圈问题
        )
    }

    // === 动作删除 ===

    fun handleRemoveExercise(
        intent: TemplateEditContract.Intent.RemoveExercise,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(
                exercises = state.exercises.filterNot { it.id == intent.exerciseId },
                hasUnsavedChanges = true,
                // 🔥 修复：不再触发自动保存，避免转圈圈问题
            ),
        )
    }

    // === 快速操作 ===

    fun handleQuickDuplicateExercise(
        intent: TemplateEditContract.Intent.QuickDuplicateExercise,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        val exerciseToDuplicate = state.exercises.find { it.id == intent.exerciseId }
            ?: return ReduceResult.noChange(state)

        val duplicatedExercise = exerciseToDuplicate.copy(
            id = "temp_${System.currentTimeMillis()}_${exerciseToDuplicate.id}",
            exerciseName = "${exerciseToDuplicate.exerciseName} (副本)",
        )

        val updatedExercises = state.exercises.toMutableList().apply {
            val originalIndex = indexOfFirst { it.id == intent.exerciseId }
            add(originalIndex + 1, duplicatedExercise)
        }

        return ReduceResult.stateOnly(
            state.copy(
                exercises = updatedExercises,
                hasUnsavedChanges = true,
                showQuickActions = false,
                quickActionTargetId = null,
            ),
        )
    }

    fun handleQuickDeleteExercise(
        intent: TemplateEditContract.Intent.QuickDeleteExercise,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        val updatedExercises = state.exercises.filter { it.id != intent.exerciseId }

        return ReduceResult.stateOnly(
            state.copy(
                exercises = updatedExercises,
                hasUnsavedChanges = true,
                showQuickActions = false,
                quickActionTargetId = null,
            ),
        )
    }

    // === 工具函数 ===

    /**
     * 将Exercise转换为TemplateExerciseDto
     */
    private fun mapExerciseToDto(
        exercise: com.example.gymbro.domain.exercise.model.Exercise,
    ): com.example.gymbro.shared.models.workout.TemplateExerciseDto {
        // 🔥 优化：只在添加动作失败或需要调试时记录
        if (extractTextSafely(exercise.name).isBlank()) {
            Timber.tag("WK-EXERCISE").w("⚠️ [ADD-SOURCE] 动作名称为空: ${exercise.id}")
        }

        // 为新添加的动作生成默认的 customSets
        val defaultCustomSets = (1..3).map { setNumber ->
            com.example.gymbro.shared.models.workout.TemplateSetDto(
                setNumber = setNumber,
                targetWeight = 0f,
                targetReps = 10,
                restTimeSeconds = 60,
                targetDuration = null,
                rpe = null,
            )
        }

        val resultDto = com.example.gymbro.shared.models.workout.TemplateExerciseDto(
            id = com.example.gymbro.shared.models.workout.WorkoutTemplateDto.generateId(),
            exerciseId = exercise.id,
            exerciseName = extractTextSafely(exercise.name),
            // 🔥 关键修复：为动作库数据提供默认值，避免无限加载
            imageUrl = exercise.imageUrl ?: "https://example.com/default_exercise.jpg",
            videoUrl = exercise.videoUrl ?: "https://example.com/default_exercise.mp4",
            sets = 3,
            reps = 10,
            rpe = null,
            targetWeight = 0f,
            restTimeSeconds = 60,
            notes = null,
            customSets = defaultCustomSets,
        )

        // 🔥 优化：只在转换失败或数据缺失时记录
        if (resultDto.exerciseName.isBlank() || (resultDto.imageUrl == null && resultDto.videoUrl == null)) {
            Timber.tag("WK-EXERCISE").w(
                "⚠️ [ADD-RESULT] 转换完成但数据可能缺失: ${resultDto.exerciseName}, imageUrl=${resultDto.imageUrl}, videoUrl=${resultDto.videoUrl}",
            )
        }

        return resultDto
    }

    /**
     * 安全提取UiText中的文本内容
     */
    private fun extractTextSafely(uiText: com.example.gymbro.core.ui.text.UiText): String {
        return when (uiText) {
            is com.example.gymbro.core.ui.text.UiText.DynamicString -> uiText.value
            is com.example.gymbro.core.ui.text.UiText.StringResource -> "Exercise_${uiText.resId}"
            is com.example.gymbro.core.ui.text.UiText.ErrorCode -> uiText.errorCode.code
            is com.example.gymbro.core.ui.text.UiText.Empty -> "未命名动作"
        }
    }
}
