package com.example.gymbro.core.network.state

import com.example.gymbro.core.network.monitor.NetworkMonitor
import com.example.gymbro.core.network.state.ConnectionState
import com.example.gymbro.shared.models.network.NetworkResult
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

/**
 * 网络状态监控器扩展接口
 * 
 * 在原有NetworkMonitor基础上，添加应用层连接状态管理能力
 * 结合系统网络监控和应用层连接监控，提供全面的网络状态管理
 */
interface NetworkStateMonitor : NetworkMonitor {

    /**
     * 流连接状态 - 应用层连接状态（如SSE、WebSocket连接）
     */
    val streamConnectionState: StateFlow<ConnectionState>

    /**
     * 聚合网络状态 - 综合系统网络和应用连接的整体评估
     */
    val aggregatedNetworkHealth: StateFlow<NetworkHealthStatus>

    /**
     * 更新流连接状态
     * 
     * @param state 新的连接状态
     */
    fun updateStreamConnectionState(state: ConnectionState)

    /**
     * 检查网络连通性
     * 
     * @return 网络连通性检查结果的Flow
     */
    fun checkConnectivity(): Flow<NetworkResult<ConnectivityCheckResult>>

    /**
     * 评估连接质量
     * 
     * @return 连接质量评估结果
     */
    suspend fun evaluateConnectionQuality(): NetworkResult<ConnectionQualityMetrics>

    /**
     * 注册流客户端状态提供者
     * 
     * @param provider 流客户端状态提供者
     */
    fun registerStreamClientProvider(provider: StreamClientStateProvider)

    /**
     * 注销流客户端状态提供者
     * 
     * @param provider 流客户端状态提供者
     */
    fun unregisterStreamClientProvider(provider: StreamClientStateProvider)
}

/**
 * 网络健康状态
 * 
 * 综合系统网络状态和应用层连接状态的整体评估
 */
data class NetworkHealthStatus(
    val systemNetworkAvailable: Boolean,
    val applicationConnectionHealthy: Boolean,
    val overallHealthy: Boolean,
    val networkType: com.example.gymbro.core.network.monitor.NetworkType,
    val connectionQuality: ConnectionQuality,
    val lastUpdated: Long = System.currentTimeMillis()
) {
    /**
     * 是否可以进行网络操作
     */
    val canPerformNetworkOperations: Boolean
        get() = systemNetworkAvailable && applicationConnectionHealthy

    /**
     * 是否需要显示网络警告
     */
    val shouldShowNetworkWarning: Boolean
        get() = !overallHealthy || connectionQuality == ConnectionQuality.Poor
}

/**
 * 连通性检查结果
 */
data class ConnectivityCheckResult(
    val isConnected: Boolean,
    val latencyMs: Long,
    val checkTime: Long = System.currentTimeMillis(),
    val endpoint: String = "",
    val errorMessage: String? = null
)

/**
 * 连接质量指标
 */
data class ConnectionQualityMetrics(
    val quality: ConnectionQuality,
    val latencyMs: Long,
    val bandwidthKbps: Int,
    val packetLossRate: Double = 0.0,
    val jitterMs: Long = 0L,
    val measurementTime: Long = System.currentTimeMillis()
) {
    /**
     * 是否为高质量连接
     */
    val isHighQuality: Boolean
        get() = quality in listOf(ConnectionQuality.Excellent, ConnectionQuality.Good)

    /**
     * 是否适合流式传输
     */
    val isSuitableForStreaming: Boolean
        get() = latencyMs < 1000 && bandwidthKbps > 500 && packetLossRate < 0.05
}