package com.example.gymbro.core.network.integration

import com.example.gymbro.core.network.security.StringXmlEscaper
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * StringXmlEscaper 集成测试
 *
 * 验证 StringXmlEscaper 在实际 Token 处理场景中的表现
 */
class StringXmlEscaperIntegrationTest {

    private lateinit var stringXmlEscaper: StringXmlEscaper

    @BeforeEach
    fun setUp() {
        stringXmlEscaper = StringXmlEscaper()
    }

    @Test
    @DisplayName("应该正确处理 JSON SSE 到 XML 的转换场景")
    fun `should correctly handle JSON SSE to XML conversion scenario`() {
        // 模拟从 JSON SSE 中提取的 reasoning_content
        val reasoningContent = "<phase:PLAN>我们需要分析这个问题 & 制定解决方案</phase:PLAN>"

        // 使用 StringXmlEscaper 处理
        val cleanedContent = stringXmlEscaper.sanitizeAndEscape(reasoningContent)

        // 验证结果
        val expected = "我们需要分析这个问题 &amp; 制定解决方案"
        assertEquals(expected, cleanedContent)

        // 验证原始内容确实包含非法标签
        assertTrue(stringXmlEscaper.hasIllegalTags(reasoningContent))

        // 验证清理后的内容不包含非法标签
        assertFalse(stringXmlEscaper.hasIllegalTags(cleanedContent))
    }

    @Test
    @DisplayName("应该正确处理复杂的 AI 输出场景")
    fun `should correctly handle complex AI output scenario`() {
        // 模拟复杂的 AI 输出，包含多种非法标签和特殊字符
        val complexContent = """
            <phase:ANALYSIS>
            让我们分析一下这个问题：
            1. 用户需求 & 约束条件
            2. 技术方案 <选择>
            3. 实施计划 "详细步骤"
            </phase:ANALYSIS>
            
            <phase:SOLUTION>
            解决方案包括：
            - 前端优化 & 后端改进
            - 数据库设计 <重要>
            - API 接口 'v2.0'
            </phase:SOLUTION>
        """.trimIndent()

        // 使用 StringXmlEscaper 处理
        val cleanedContent = stringXmlEscaper.sanitizeAndEscape(complexContent)

        // 验证所有非法标签都被清理
        assertFalse(cleanedContent.contains("<phase:"))
        assertFalse(cleanedContent.contains("</phase:"))

        // 验证特殊字符被正确转义
        assertTrue(cleanedContent.contains("&amp;"))
        assertTrue(cleanedContent.contains("&lt;"))
        assertTrue(cleanedContent.contains("&gt;"))
        assertTrue(cleanedContent.contains("&quot;"))
        assertTrue(cleanedContent.contains("&#39;"))

        // 验证文本内容被保留
        assertTrue(cleanedContent.contains("让我们分析一下这个问题"))
        assertTrue(cleanedContent.contains("解决方案包括"))
        assertTrue(cleanedContent.contains("前端优化"))
        assertTrue(cleanedContent.contains("API 接口"))
    }

    @Test
    @DisplayName("应该正确处理 ThinkingBox 期望的 XML 格式")
    fun `should correctly handle ThinkingBox expected XML format`() {
        // 模拟 AdaptiveStreamClient 需要生成的 XML 格式
        val reasoningContent = "<phase:PLAN>制定训练计划</phase:PLAN>"
        val content = "基于您的需求，我建议以下训练方案..."

        // 处理 reasoning_content（应该包装在 <think> 标签中）
        val cleanedReasoning = stringXmlEscaper.sanitizeAndEscape(reasoningContent)
        val thinkXml = "<think>$cleanedReasoning"

        // 处理 content（应该关闭 <think> 标签）
        val cleanedContent = stringXmlEscaper.sanitizeAndEscape(content)
        val finalXml = "</think>$cleanedContent"

        // 验证生成的 XML 格式
        assertEquals("<think>制定训练计划", thinkXml)
        assertEquals("</think>基于您的需求，我建议以下训练方案...", finalXml)

        // 验证完整的 XML 结构
        val completeXml = thinkXml + finalXml
        assertTrue(completeXml.startsWith("<think>"))
        assertTrue(completeXml.contains("</think>"))
        assertFalse(completeXml.contains("<phase:"))
    }

    @Test
    @DisplayName("应该正确处理边界情况和错误输入")
    fun `should correctly handle edge cases and error inputs`() {
        // 测试空字符串
        assertEquals("", stringXmlEscaper.sanitizeAndEscape(""))

        // 测试只包含空白字符
        val whitespace = "   \n\t  "
        assertEquals(whitespace, stringXmlEscaper.sanitizeAndEscape(whitespace))

        // 测试不完整的标签
        val incompleteTag = "开始内容<phase:PL"
        val cleanedIncomplete = stringXmlEscaper.sanitizeAndEscape(incompleteTag)
        assertEquals("开始内容", cleanedIncomplete)

        // 测试混合合法和非法标签
        val mixedTags = "<phase:PLAN>非法内容</phase:PLAN><thinking>合法内容</thinking>"
        val cleanedMixed = stringXmlEscaper.sanitizeAndEscape(mixedTags)
        assertEquals("非法内容&lt;thinking&gt;合法内容&lt;/thinking&gt;", cleanedMixed)
    }

    @Test
    @DisplayName("应该提供准确的清理统计信息")
    fun `should provide accurate cleaning statistics`() {
        val originalContent = "<phase:PLAN>测试内容 & 特殊字符</phase:PLAN>额外文本"
        val cleanedContent = stringXmlEscaper.sanitizeAndEscape(originalContent)

        val stats = stringXmlEscaper.getCleaningStats(originalContent, cleanedContent)

        // 验证统计信息
        assertEquals(originalContent.length, stats.originalLength)
        assertEquals(cleanedContent.length, stats.cleanedLength)
        assertTrue(stats.hasEffectiveCleaning)
        assertTrue(stats.cleaningEfficiency > 0)
        assertTrue(stats.illegalTagCount > 0)

        // 验证清理效果
        assertTrue(stats.removedChars > 0) // 应该移除了一些字符（标签）
    }

    @Test
    @DisplayName("应该正确处理高频调用的轻量级清理")
    fun `should correctly handle lightweight cleaning for high frequency calls`() {
        // 测试包含控制字符的 token
        val dirtyToken = "Hello\u0000World\uFFFD\u0001Test\u007F"
        val cleanToken = stringXmlEscaper.sanitizeToken(dirtyToken)

        // 验证控制字符被移除
        assertEquals("HelloWorldTest", cleanToken)

        // 验证正常字符不受影响
        val normalToken = "Hello World Test"
        assertEquals(normalToken, stringXmlEscaper.sanitizeToken(normalToken))
    }

    @Test
    @DisplayName("应该保持性能在可接受范围内")
    fun `should maintain acceptable performance`() {
        // 创建一个较大的测试内容
        val largeContent = buildString {
            repeat(100) {
                append("<phase:PLAN>这是第${it}个测试段落，包含特殊字符 & 符号 <test> \"引号\" 'apostrophe'</phase:PLAN>\n")
            }
        }

        // 测量处理时间
        val startTime = System.currentTimeMillis()
        val result = stringXmlEscaper.sanitizeAndEscape(largeContent)
        val endTime = System.currentTimeMillis()

        val processingTime = endTime - startTime

        // 验证处理时间在合理范围内（应该小于100ms）
        assertTrue(processingTime < 100, "处理时间过长: ${processingTime}ms")

        // 验证结果正确性
        assertFalse(result.contains("<phase:"))
        assertTrue(result.contains("&amp;"))
        assertTrue(result.contains("&lt;"))
        assertTrue(result.contains("&quot;"))
    }
}
