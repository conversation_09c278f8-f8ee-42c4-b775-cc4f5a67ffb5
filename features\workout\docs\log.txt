09:13:13.341 System.out               I  🔧 [DEBUG] KeypadInputField(重量) 被点击: exerciseId=1, setIndex=3
09:13:13.342 TB                       D  🔧 [WorkoutExerciseComponent] 组件***-***-****-***-***-****c-***-***-****af-a3b9-***-***-****ee0***-***-****c9c6 显示数字键盘: field=InputTargetField(exerciseId=1, setIndex=3, fieldType=WEIGHT), 原始值=0.0, 键盘初始值=
09:13:13.751 InsetsController         D  hide(ime(), fromIme=false)
09:13:13.751 ImeTracker               I  com.example.gymbro:501b8e0a: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
09:13:14.501 TB                       D  🔧 [JSON-DEBUG] 转换前state.exercise: 组数=4
09:13:14.501                          D  🔧 [JSON-DEBUG] 转换前组1: id=1, weight=5.0, reps=***-***-****
09:13:14.502                          D  🔧 [JSON-DEBUG] 转换前组2: id=2, weight=5.0, reps=***-***-****
09:13:14.502                          D  🔧 [JSON-DEBUG] 转换前组3: id=3, weight=0.0, reps=***-***-****
09:13:14.502                          D  🔧 [JSON-DEBUG] 转换前组4: id=bc***-***-****-***-***-****b-4dee-***-***-****d3-b***-***-****a***-***-****a***-***-****, weight=***-***-****.0, reps=***-***-****
09:13:14.503                          D  🔧 [JSON-DEBUG] 转换后JSON长度: ***-***-****
09:13:14.503                          D  🔧 [WorkoutExerciseComponent] 单个数据单元更新开始: 组4, 字段=WEIGHT, setId=bc***-***-****-***-***-****b-4dee-***-***-****d3-b***-***-****a***-***-****a***-***-****, 值=1.0
09:13:14.506                          D  🔧 [WEIGHT-UPDATE] 开始更新重量: setId=bc***-***-****-***-***-****b-4dee-***-***-****d3-b***-***-****a***-***-****a***-***-****, newWeight=1.0
09:13:14.506                          D  🔧 [WEIGHT-UPDATE] 当前exercise有4组
09:13:14.506                          D  🔧 [WEIGHT-UPDATE] 组1: id=1, weight=5.0
09:13:14.507                          D  🔧 [WEIGHT-UPDATE] 组2: id=2, weight=5.0
09:13:14.508                          D  🔧 [WEIGHT-UPDATE] 组3: id=3, weight=0.0
09:13:14.509                          D  🔧 [WEIGHT-UPDATE] 组4: id=bc***-***-****-***-***-****b-4dee-***-***-****d3-b***-***-****a***-***-****a***-***-****, weight=***-***-****.0
09:13:14.509                          D  🔧 [WEIGHT-UPDATE] 找到匹配组: bc***-***-****-***-***-****b-4dee-***-***-****d3-b***-***-****a***-***-****a***-***-****, 更新重量 ***-***-****.0 -> 1.0
09:13:14.514                          D  🔧 [WEIGHT-UPDATE] 验证结果: 组bc***-***-****-***-***-****b-4dee-***-***-****d3-b***-***-****a***-***-****a***-***-****的重量为1.0
09:13:14.515                          D  🔧 [JSON-DEBUG] 更新后JSON长度: ***-***-****
09:13:14.518                          D  🔧 [JSON-DEBUG] 反序列化后exercise: 组数=4
09:13:14.518                          D  🔧 [JSON-DEBUG] 验证后组1: id=1, weight=5.0, reps=***-***-****, rest=***-***-****s
09:13:14.519                          D  🔧 [JSON-DEBUG] 验证后组2: id=2, weight=5.0, reps=***-***-****, rest=***-***-****s
09:13:14.519                          D  🔧 [JSON-DEBUG] 验证后组3: id=3, weight=0.0, reps=***-***-****, rest=***-***-****s
09:13:14.520                          D  🔧 [JSON-DEBUG] 验证后组4: id=bc***-***-****-***-***-****b-4dee-***-***-****d3-b***-***-****a***-***-****a***-***-****, weight=1.0, reps=***-***-****, rest=***-***-****s
09:13:14.520                          D  🔧 [WorkoutExerciseComponent] 单个数据单元更新完成: 动作=杠铃卧推, 组数=4
09:13:14.521                          D  🔧 [WorkoutExerciseComponent] 更新后组4数据: weight=1.0, reps=***-***-****, rest=***-***-****s
09:13:14.522                          D  🔧 [DEBUG-SAVE] WorkoutExerciseComponent.updateTargetFieldDirectly 即将调用 onExerciseUpdate:
09:13:14.522                          D  🔧 [DEBUG-SAVE] 动作=杠铃卧推, targetSets=4
09:13:14.522                          D  🔧 [DEBUG-SAVE] 输出组1: weight=5.0, reps=***-***-****, rest=***-***-****s, id=1
09:13:14.523                          D  🔧 [DEBUG-SAVE] 输出组2: weight=5.0, reps=***-***-****, rest=***-***-****s, id=2
09:13:14.523                          D  🔧 [DEBUG-SAVE] 输出组3: weight=0.0, reps=***-***-****, rest=***-***-****s, id=3
09:13:14.524                          D  🔧 [DEBUG-SAVE] 输出组4: weight=1.0, reps=***-***-****, rest=***-***-****s, id=bc***-***-****-***-***-****b-4dee-***-***-****d3-b***-***-****a***-***-****a***-***-****
09:13:14.524                          D  🔧 [UPDATE] DraggableExerciseCard 收到更新: 杠铃卧推, sets=4
09:13:14.525                          D  🔄 [TemplateExerciseConverter] 更新动作数据: 杠铃卧推
09:13:14.526                          D  🔄 [TemplateExerciseConverter] 原customSets数量: 3, 新targetSets数量: 4
09:13:14.527                          D  🔄 [TemplateExerciseConverter] 更新后customSets数量: 4
09:13:14.527                          D  🔄 [TemplateExerciseConverter] 组1: weight=5.0, reps=***-***-****, rest=***-***-****s
09:13:14.528                          D  🔄 [TemplateExerciseConverter] 组2: weight=5.0, reps=***-***-****, rest=***-***-****s
09:13:14.528                          D  🔄 [TemplateExerciseConverter] 组3: weight=0.0, reps=***-***-****, rest=***-***-****s
09:13:14.529                          D  🔄 [TemplateExerciseConverter] 组4: weight=1.0, reps=***-***-****, rest=***-***-****s
09:13:14.529                          D  🔧 [UPDATE] DraggableExerciseCard 调用 onExerciseUpdate
09:13:14.530                          D  🎯 [RENDER-DEBUG] TemplateEditor 收到动作更新: 杠铃卧推
09:13:14.530                          D  🎯 [TemplateEditViewModel] Processing: UpdateExercise
09:13:14.531 WK-CORE                  D  🎯 处理Intent: UpdateExercise
09:13:14.531 TB                       D  🔥 [WK-UPDATE-EXERCISE] 动作更新被触发: 杠铃卧推
09:13:14.531                          D  🔥 [WK-UPDATE-EXERCISE] customSets数量: 4
09:13:14.532 WK-EXERCISE              D  🔥 handleUpdateExercise 被调用: 动作=杠铃卧推, customSets=4
09:13:14.532                          D  🔥 Intent组1: weight=5.0, reps=10, rest=60s
09:13:14.532                          D  🔥 Intent组2: weight=5.0, reps=10, rest=60s
09:13:14.534                          D  🔥 Intent组3: weight=0.0, reps=10, rest=60s
09:13:14.534                          D  🔥 Intent组4: weight=1.0, reps=10, rest=60s
09:13:14.534 TB                       D  🔥 [WK-UPDATE-EXERCISE] 即将触发TriggerAutoSave Effect
09:13:14.535                          D  🔥 [WK-UPDATE-EXERCISE] 注意: 自动保存功能已被禁用，只会记录日志
09:13:14.536                          D  🔧 [DEBUG-SAVE] onExerciseUpdate 调用完成: field=WEIGHT, setId=bc***-***-****-***-***-****b-4dee-***-***-****d3-b***-***-****a***-***-****a***-***-****, value=1.0
09:13:14.536                          D  🔧 [WorkoutExerciseComponent] 键盘输入: 1 -> 直接更新原始输入框
09:13:14.563                          D  🎯 [RENDER-DEBUG] TemplateEditor 开始渲染 4 个动作
09:13:14.796                          D  🔧 [JSON-DEBUG] 转换前state.exercise: 组数=4
09:13:14.798                          D  🔧 [JSON-DEBUG] 转换前组1: id=1, weight=5.0, reps=***-***-****
09:13:14.800                          D  🔧 [JSON-DEBUG] 转换前组2: id=2, weight=5.0, reps=***-***-****
09:13:14.801                          D  🔧 [JSON-DEBUG] 转换前组3: id=3, weight=0.0, reps=***-***-****
09:13:14.801                          D  🔧 [JSON-DEBUG] 转换前组4: id=bc***-***-****-***-***-****b-4dee-***-***-****d3-b***-***-****a***-***-****a***-***-****, weight=1.0, reps=***-***-****
09:13:14.802                          D  🔧 [JSON-DEBUG] 转换后JSON长度: ***-***-****
09:13:14.802                          D  🔧 [WorkoutExerciseComponent] 单个数据单元更新开始: 组4, 字段=WEIGHT, setId=bc***-***-****-***-***-****b-4dee-***-***-****d3-b***-***-****a***-***-****a***-***-****, 值=***-***-****.0
09:13:14.808                          D  🔧 [WEIGHT-UPDATE] 开始更新重量: setId=bc***-***-****-***-***-****b-4dee-***-***-****d3-b***-***-****a***-***-****a***-***-****, newWeight=***-***-****.0
09:13:14.809                          D  🔧 [WEIGHT-UPDATE] 当前exercise有4组
09:13:14.810                          D  🔧 [WEIGHT-UPDATE] 组1: id=1, weight=5.0
09:13:14.812                          D  🔧 [WEIGHT-UPDATE] 组2: id=2, weight=5.0
09:13:14.813                          D  🔧 [WEIGHT-UPDATE] 组3: id=3, weight=0.0
09:13:14.814                          D  🔧 [WEIGHT-UPDATE] 组4: id=bc***-***-****-***-***-****b-4dee-***-***-****d3-b***-***-****a***-***-****a***-***-****, weight=1.0
09:13:14.814                          D  🔧 [WEIGHT-UPDATE] 找到匹配组: bc***-***-****-***-***-****b-4dee-***-***-****d3-b***-***-****a***-***-****a***-***-****, 更新重量 1.0 -> ***-***-****.0
09:13:14.820                          D  🔧 [WEIGHT-UPDATE] 验证结果: 组bc***-***-****-***-***-****b-4dee-***-***-****d3-b***-***-****a***-***-****a***-***-****的重量为***-***-****.0
09:13:14.820                          D  🔧 [JSON-DEBUG] 更新后JSON长度: ***-***-****
09:13:14.825                          D  🔧 [JSON-DEBUG] 反序列化后exercise: 组数=4
09:13:14.825                          D  🔧 [JSON-DEBUG] 验证后组1: id=1, weight=5.0, reps=***-***-****, rest=***-***-****s
09:13:14.827                          D  🔧 [JSON-DEBUG] 验证后组2: id=2, weight=5.0, reps=***-***-****, rest=***-***-****s
09:13:14.828                          D  🔧 [JSON-DEBUG] 验证后组3: id=3, weight=0.0, reps=***-***-****, rest=***-***-****s
09:13:14.830                          D  🔧 [JSON-DEBUG] 验证后组4: id=bc***-***-****-***-***-****b-4dee-***-***-****d3-b***-***-****a***-***-****a***-***-****, weight=***-***-****.0, reps=***-***-****, rest=***-***-****s
09:13:14.832                          D  🔧 [WorkoutExerciseComponent] 单个数据单元更新完成: 动作=杠铃卧推, 组数=4
09:13:14.832                          D  🔧 [WorkoutExerciseComponent] 更新后组4数据: weight=***-***-****.0, reps=***-***-****, rest=***-***-****s
09:13:14.833                          D  🔧 [DEBUG-SAVE] WorkoutExerciseComponent.updateTargetFieldDirectly 即将调用 onExerciseUpdate:
09:13:14.833                          D  🔧 [DEBUG-SAVE] 动作=杠铃卧推, targetSets=4
09:13:14.833                          D  🔧 [DEBUG-SAVE] 输出组1: weight=5.0, reps=***-***-****, rest=***-***-****s, id=1
09:13:14.833                          D  🔧 [DEBUG-SAVE] 输出组2: weight=5.0, reps=***-***-****, rest=***-***-****s, id=2
09:13:14.834                          D  🔧 [DEBUG-SAVE] 输出组3: weight=0.0, reps=***-***-****, rest=***-***-****s, id=3
09:13:14.835                          D  🔧 [DEBUG-SAVE] 输出组4: weight=***-***-****.0, reps=***-***-****, rest=***-***-****s, id=bc***-***-****-***-***-****b-4dee-***-***-****d3-b***-***-****a***-***-****a***-***-****
09:13:14.836                          D  🔧 [UPDATE] DraggableExerciseCard 收到更新: 杠铃卧推, sets=4
09:13:14.837                          D  🔄 [TemplateExerciseConverter] 更新动作数据: 杠铃卧推
09:13:14.839                          D  🔄 [TemplateExerciseConverter] 原customSets数量: 3, 新targetSets数量: 4
09:13:14.840                          D  🔄 [TemplateExerciseConverter] 更新后customSets数量: 4
09:13:14.840                          D  🔄 [TemplateExerciseConverter] 组1: weight=5.0, reps=***-***-****, rest=***-***-****s
09:13:14.841                          D  🔄 [TemplateExerciseConverter] 组2: weight=5.0, reps=***-***-****, rest=***-***-****s
09:13:14.844                          D  🔄 [TemplateExerciseConverter] 组3: weight=0.0, reps=***-***-****, rest=***-***-****s
09:13:14.845                          D  🔄 [TemplateExerciseConverter] 组4: weight=***-***-****.0, reps=***-***-****, rest=***-***-****s
09:13:14.847                          D  🔧 [UPDATE] DraggableExerciseCard 调用 onExerciseUpdate
09:13:14.850                          D  🎯 [RENDER-DEBUG] TemplateEditor 收到动作更新: 杠铃卧推
09:13:14.851                          D  🎯 [TemplateEditViewModel] Processing: UpdateExercise
09:13:14.852 WK-CORE                  D  🎯 处理Intent: UpdateExercise
09:13:14.854 TB                       D  🔥 [WK-UPDATE-EXERCISE] 动作更新被触发: 杠铃卧推
09:13:14.854                          D  🔥 [WK-UPDATE-EXERCISE] customSets数量: 4
09:13:14.854 WK-EXERCISE              D  🔥 handleUpdateExercise 被调用: 动作=杠铃卧推, customSets=4
09:13:14.855                          D  🔥 Intent组1: weight=5.0, reps=10, rest=60s
09:13:14.855                          D  🔥 Intent组2: weight=5.0, reps=10, rest=60s
09:13:14.855                          D  🔥 Intent组3: weight=0.0, reps=10, rest=60s
09:13:14.855                          D  🔥 Intent组4: weight=10.0, reps=10, rest=60s
09:13:14.856 TB                       D  🔥 [WK-UPDATE-EXERCISE] 即将触发TriggerAutoSave Effect
09:13:14.858                          D  🔥 [WK-UPDATE-EXERCISE] 注意: 自动保存功能已被禁用，只会记录日志
09:13:14.859                          D  🔧 [DEBUG-SAVE] onExerciseUpdate 调用完成: field=WEIGHT, setId=bc***-***-****-***-***-****b-4dee-***-***-****d3-b***-***-****a***-***-****a***-***-****, value=***-***-****.0
09:13:14.860                          D  🔧 [WorkoutExerciseComponent] 键盘输入: ***-***-**** -> 直接更新原始输入框
09:13:14.881                          D  🎯 [RENDER-DEBUG] TemplateEditor 开始渲染 4 个动作
09:13:15.483                          D  🔧 [WorkoutExerciseComponent] 整列复制: fieldType=WEIGHT, direction=UP, 当前组=4
09:13:15.487                          D  🔧 [UPDATE] DraggableExerciseCard 收到更新: 杠铃卧推, sets=4
09:13:15.488                          D  🔄 [TemplateExerciseConverter] 更新动作数据: 杠铃卧推
09:13:15.489                          D  🔄 [TemplateExerciseConverter] 原customSets数量: 4, 新targetSets数量: 4
09:13:15.491                          D  🔄 [TemplateExerciseConverter] 更新后customSets数量: 4
09:13:15.492                          D  🔄 [TemplateExerciseConverter] 组1: weight=0.0, reps=***-***-****, rest=***-***-****s
09:13:15.493                          D  🔄 [TemplateExerciseConverter] 组2: weight=0.0, reps=***-***-****, rest=***-***-****s
09:13:15.494                          D  🔄 [TemplateExerciseConverter] 组3: weight=0.0, reps=***-***-****, rest=***-***-****s
09:13:15.495                          D  🔄 [TemplateExerciseConverter] 组4: weight=0.0, reps=***-***-****, rest=***-***-****s
09:13:15.495                          D  🔧 [UPDATE] DraggableExerciseCard 调用 onExerciseUpdate
09:13:15.496                          D  🎯 [RENDER-DEBUG] TemplateEditor 收到动作更新: 杠铃卧推
09:13:15.498                          D  🎯 [TemplateEditViewModel] Processing: UpdateExercise
09:13:15.499 WK-CORE                  D  🎯 处理Intent: UpdateExercise
09:13:15.500 TB                       D  🔥 [WK-UPDATE-EXERCISE] 动作更新被触发: 杠铃卧推
09:13:15.500                          D  🔥 [WK-UPDATE-EXERCISE] customSets数量: 4
09:13:15.500 WK-EXERCISE              D  🔥 handleUpdateExercise 被调用: 动作=杠铃卧推, customSets=4
09:13:15.501                          D  🔥 Intent组1: weight=0.0, reps=10, rest=60s
09:13:15.502                          D  🔥 Intent组2: weight=0.0, reps=10, rest=60s
09:13:15.502                          D  🔥 Intent组3: weight=0.0, reps=10, rest=60s
09:13:15.503                          D  🔥 Intent组4: weight=0.0, reps=10, rest=60s
09:13:15.503 TB                       D  🔥 [WK-UPDATE-EXERCISE] 即将触发TriggerAutoSave Effect
09:13:15.503                          D  🔥 [WK-UPDATE-EXERCISE] 注意: 自动保存功能已被禁用，只会记录日志
09:13:15.505                          D  🔧 [WorkoutExerciseComponent] 整列复制成功: 从第4组复制重量0.0kg到上面所有组(共3组)
09:13:15.506                          D  🔧 [WorkoutExerciseComponent] 组件***-***-****-***-***-****c-***-***-****af-a3b9-***-***-****ee0***-***-****c9c6 隐藏内部Keypad
09:13:15.520                          D  🎯 [RENDER-DEBUG] TemplateEditor 开始渲染 4 个动作
09:13:15.523 WindowOnBackDispatcher   W  sendCancelIfRunning: isInProgress=false callback=androidx.compose.material3.ModalBottomSheetDialogLayout$Api34Impl$createBackCallback$1@78bcc65
09:13:15.602 InsetsController         D  hide(ime(), fromIme=false)
09:13:15.603 ImeTracker               I  com.example.gymbro:848d44af: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
