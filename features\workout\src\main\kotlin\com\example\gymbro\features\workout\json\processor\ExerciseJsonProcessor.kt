package com.example.gymbro.features.workout.json.processor

import com.example.gymbro.features.workout.json.core.JsonConstants
import com.example.gymbro.shared.models.exercise.ExerciseDto
import com.example.gymbro.shared.models.exercise.ExerciseSetDto
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import timber.log.Timber

/**
 * 组数据更新类型
 */
enum class SetUpdateType {
    WEIGHT, REPS, COMPLETION, REST_TIME
}

/**
 * 组数据更新数据类
 */
data class SetUpdateData(
    val setId: String,
    val type: SetUpdateType,
    val weightValue: Float? = null,
    val repsValue: Int? = null,
    val completedValue: Boolean? = null,
    val restTimeValue: Int? = null,
)

/**
 * Exercise JSON 处理器
 *
 * 专门处理单个动作的 JSON 转换和数据处理
 * 支持 WorkoutExerciseComponent 的动态数据修改和实时更新
 *
 * 核心功能：
 * - Exercise 数据的 JSON 序列化/反序列化
 * - 动态字段修改（重量、次数、组数等）
 * - 实时数据更新和同步
 * - 数据完整性验证
 * - 错误处理和容错机制
 *
 * <AUTHOR> AI Assistant
 * @since 1.0.0
 */
object ExerciseJsonProcessor {

    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
        isLenient = true
        prettyPrint = false
    }

    // ==================== 核心转换方法 ====================

    /**
     * ExerciseDto → JSON 字符串
     */
    fun ExerciseDto.toJson(): String {
        return try {
            val jsonString = json.encodeToString(this)

            // 验证序列化结果
            if (jsonString.length > JsonConstants.MAX_JSON_SIZE) {
                Timber.w("Exercise JSON 过大: ${jsonString.length} 字符")
            }

            jsonString
        } catch (e: Exception) {
            Timber.e(e, "Exercise 序列化失败: ${this.name}")
            createFallbackExerciseJson(this)
        }
    }

    /**
     * JSON 字符串 → ExerciseDto
     */
    fun fromJson(jsonString: String): ExerciseDto? {
        return try {
            val exercise = json.decodeFromString<ExerciseDto>(jsonString)
            exercise
        } catch (e: Exception) {
            Timber.e(e, "Exercise 反序列化失败")
            null
        }
    }

    /**
     * 批量转换 ExerciseDto 列表 → JSON 数组字符串
     */
    fun List<ExerciseDto>.toJsonArray(): String {
        return try {
            json.encodeToString(this)
        } catch (e: Exception) {
            Timber.e(e, "Exercise 列表序列化失败")
            "[]"
        }
    }

    /**
     * JSON 数组字符串 → ExerciseDto 列表
     */
    fun fromJsonArray(jsonString: String): List<ExerciseDto> {
        return try {
            json.decodeFromString<List<ExerciseDto>>(jsonString)
        } catch (e: Exception) {
            Timber.e(e, "Exercise 列表反序列化失败")
            emptyList()
        }
    }

    // ==================== 动态数据修改方法 ====================

    /**
     * 更新 Exercise 的重量数据
     */
    fun updateExerciseWeight(jsonString: String, setId: String, newWeight: Float): String {
        return try {
            val exercise = fromJson(jsonString) ?: return jsonString

            // 🔥 调试：添加setId匹配检查日志
            Timber.d("🔧 [WEIGHT-UPDATE] 开始更新重量: setId=$setId, newWeight=$newWeight")
            Timber.d("🔧 [WEIGHT-UPDATE] 当前exercise有${exercise.targetSets.size}组")
            exercise.targetSets.forEachIndexed { index, set ->
                Timber.d("🔧 [WEIGHT-UPDATE] 组${index + 1}: id=${set.id}, weight=${set.weight}")
            }

            val updatedSets = exercise.targetSets.map { set ->
                if (set.id == setId) {
                    Timber.d("🔧 [WEIGHT-UPDATE] 找到匹配组: ${set.id}, 更新重量 ${set.weight} -> $newWeight")
                    set.copy(weight = newWeight)
                } else {
                    set
                }
            }

            val updatedExercise = exercise.copy(targetSets = updatedSets)
            val result = updatedExercise.toJson()

            // 🔥 验证更新结果
            val verifyExercise = fromJson(result)
            val updatedSet = verifyExercise?.targetSets?.find { it.id == setId }
            Timber.d("🔧 [WEIGHT-UPDATE] 验证结果: 组${setId}的重量为${updatedSet?.weight}")

            result
        } catch (e: Exception) {
            Timber.e(e, "更新重量失败: setId=$setId, weight=$newWeight")
            jsonString
        }
    }

    /**
     * 更新 Exercise 的次数数据
     */
    fun updateExerciseReps(jsonString: String, setId: String, newReps: Int): String {
        return try {
            val exercise = fromJson(jsonString) ?: return jsonString

            // 🔥 调试：添加setId匹配检查日志
            Timber.d("🔧 [REPS-UPDATE] 开始更新次数: setId=$setId, newReps=$newReps")
            Timber.d("🔧 [REPS-UPDATE] 当前exercise有${exercise.targetSets.size}组")
            exercise.targetSets.forEachIndexed { index, set ->
                Timber.d("🔧 [REPS-UPDATE] 组${index + 1}: id=${set.id}, reps=${set.reps}")
            }

            val updatedSets = exercise.targetSets.map { set ->
                if (set.id == setId) {
                    Timber.d("🔧 [REPS-UPDATE] 找到匹配组: ${set.id}, 更新次数 ${set.reps} -> $newReps")
                    set.copy(reps = newReps)
                } else {
                    set
                }
            }

            val updatedExercise = exercise.copy(targetSets = updatedSets)
            val result = updatedExercise.toJson()

            // 🔥 验证更新结果
            val verifyExercise = fromJson(result)
            val updatedSet = verifyExercise?.targetSets?.find { it.id == setId }
            Timber.d("🔧 [REPS-UPDATE] 验证结果: 组${setId}的次数为${updatedSet?.reps}")

            result
        } catch (e: Exception) {
            Timber.e(e, "更新次数失败: setId=$setId, reps=$newReps")
            jsonString
        }
    }

    /**
     * 更新 Exercise 的完成状态
     */
    fun updateExerciseSetCompletion(jsonString: String, setId: String, isCompleted: Boolean): String {
        return try {
            val exercise = fromJson(jsonString) ?: return jsonString

            val updatedSets = exercise.targetSets.map { set ->
                if (set.id == setId) {
                    set.copy(
                        isCompleted = isCompleted,
                        completedAt = if (isCompleted) System.currentTimeMillis() else null,
                    )
                } else {
                    set
                }
            }

            val updatedExercise = exercise.copy(targetSets = updatedSets)
            updatedExercise.toJson()
        } catch (e: Exception) {
            Timber.e(e, "更新完成状态失败: setId=$setId, completed=$isCompleted")
            jsonString
        }
    }

    /**
     * 更新单个组的休息时间
     */
    fun updateExerciseSetRestTime(jsonString: String, setId: String, newRestTimeSeconds: Int): String {
        return try {
            val exercise = fromJson(jsonString) ?: return jsonString

            val validatedRestTime = newRestTimeSeconds.coerceIn(
                JsonConstants.MIN_REST_TIME_SECONDS,
                JsonConstants.MAX_REST_TIME_SECONDS,
            )

            val updatedSets = exercise.targetSets.map { set ->
                if (set.id == setId) {
                    set.copy(restTimeSeconds = validatedRestTime)
                } else {
                    set
                }
            }

            val updatedExercise = exercise.copy(targetSets = updatedSets)
            updatedExercise.toJson()
        } catch (e: Exception) {
            Timber.e(e, "更新组休息时间失败: setId=$setId, restTime=$newRestTimeSeconds")
            jsonString
        }
    }

    /**
     * 更新 Exercise 的全局休息时间
     */
    fun updateRestTime(jsonString: String, newRestTimeSeconds: Int): String {
        return try {
            val exercise = fromJson(jsonString) ?: return jsonString

            val validatedRestTime = newRestTimeSeconds.coerceIn(
                JsonConstants.MIN_REST_TIME_SECONDS,
                JsonConstants.MAX_REST_TIME_SECONDS,
            )

            val updatedExercise = exercise.copy(restTimeSeconds = validatedRestTime)
            updatedExercise.toJson()
        } catch (e: Exception) {
            Timber.e(e, "更新休息时间失败: restTime=$newRestTimeSeconds")
            jsonString
        }
    }

    /**
     * 添加新的组到 Exercise
     */
    fun addExerciseSet(jsonString: String, newSet: ExerciseSetDto): String {
        return try {
            val exercise = fromJson(jsonString) ?: return jsonString

            val updatedSets = exercise.targetSets + newSet
            val updatedExercise = exercise.copy(targetSets = updatedSets)
            updatedExercise.toJson()
        } catch (e: Exception) {
            Timber.e(e, "添加新组失败: setId=${newSet.id}")
            jsonString
        }
    }

    /**
     * 删除 Exercise 的组
     */
    fun removeExerciseSet(jsonString: String, setId: String): String {
        return try {
            val exercise = fromJson(jsonString) ?: return jsonString

            val updatedSets = exercise.targetSets.filter { it.id != setId }

            // 确保至少保留一组
            if (updatedSets.isEmpty()) {
                Timber.w("尝试删除所有组，保留最后一组")
                return jsonString
            }

            val updatedExercise = exercise.copy(targetSets = updatedSets)
            updatedExercise.toJson()
        } catch (e: Exception) {
            Timber.e(e, "删除组失败: setId=$setId")
            jsonString
        }
    }

    // ==================== 批量更新方法 ====================

    /**
     * 批量更新 Exercise 数据
     */
    fun batchUpdateExercise(jsonString: String, updates: List<SetUpdateData>): String {
        return try {
            var currentJson = jsonString

            updates.forEach { update ->
                currentJson = when (update.type) {
                    SetUpdateType.WEIGHT ->
                        updateExerciseWeight(currentJson, update.setId, update.weightValue ?: 0f)
                    SetUpdateType.REPS ->
                        updateExerciseReps(currentJson, update.setId, update.repsValue ?: 0)
                    SetUpdateType.COMPLETION ->
                        updateExerciseSetCompletion(currentJson, update.setId, update.completedValue ?: false)
                    SetUpdateType.REST_TIME ->
                        updateRestTime(currentJson, update.restTimeValue ?: JsonConstants.DEFAULT_REST_TIME_SECONDS)
                }
            }

            currentJson
        } catch (e: Exception) {
            Timber.e(e, "批量更新失败: ${updates.size} 个更新")
            // 简化错误处理，直接返回原始数据
            jsonString
        }
    }

    // ==================== 容错处理方法 ====================

    /**
     * 创建 Exercise 的 fallback JSON
     */
    private fun createFallbackExerciseJson(exercise: ExerciseDto): String {
        return try {
            val fallbackExercise = ExerciseDto(
                id = exercise.id.takeIf { it.isNotBlank() } ?: "fallback_exercise",
                name = exercise.name.takeIf { it.isNotBlank() } ?: "未知动作",
                imageUrl = null,
                videoUrl = null,
                targetSets = listOf(
                    ExerciseSetDto(
                        id = "fallback_set",
                        weight = 0f,
                        reps = 1,
                        isCompleted = false,
                        completedAt = null,
                    ),
                ),
                restTimeSeconds = JsonConstants.DEFAULT_REST_TIME_SECONDS,
                notes = "数据恢复模式",
            )

            json.encodeToString(fallbackExercise)
        } catch (e: Exception) {
            Timber.e(e, "创建 fallback JSON 失败")
            JsonConstants.EMPTY_EXERCISE_JSON
        }
    }

    /**
     * 验证 Exercise JSON 的完整性
     */
    fun validateExerciseJson(jsonString: String): Boolean {
        return try {
            val exercise = fromJson(jsonString)
            exercise != null &&
                exercise.id.isNotBlank() &&
                exercise.name.isNotBlank() &&
                exercise.targetSets.isNotEmpty()
        } catch (e: Exception) {
            Timber.w(e, "Exercise JSON 验证失败")
            false
        }
    }
}
