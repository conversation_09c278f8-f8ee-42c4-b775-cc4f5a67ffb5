package com.example.gymbro.core.network.state

import kotlinx.coroutines.flow.StateFlow

/**
 * 连接状态定义
 * 
 * 用于追踪具体的连接状态，包括应用层的流连接状态
 */
sealed interface ConnectionState {
    /**
     * 空闲状态 - 未建立连接
     */
    data object Idle : ConnectionState

    /**
     * 连接中 - 正在尝试建立连接
     */
    data class Connecting(val attempt: Int = 1) : ConnectionState

    /**
     * 已连接 - 连接成功建立
     */
    data class Connected(
        val connectionTime: Long = System.currentTimeMillis(),
        val quality: ConnectionQuality = ConnectionQuality.Unknown
    ) : ConnectionState

    /**
     * 连接断开 - 连接已断开
     */
    data class Disconnected(
        val reason: String,
        val canRetry: Boolean = true,
        val lastConnectedTime: Long = 0L
    ) : ConnectionState

    /**
     * 连接错误 - 连接出现错误
     */
    data class Error(
        val throwable: Throwable,
        val canRetry: Boolean = true,
        val retryDelayMs: Long = 0L
    ) : ConnectionState

    /**
     * 重连中 - 正在尝试重新连接
     */
    data class Reconnecting(
        val attempt: Int,
        val maxAttempts: Int = 5,
        val nextRetryDelayMs: Long = 0L
    ) : ConnectionState
}

/**
 * 连接质量评估
 */
enum class ConnectionQuality {
    Unknown,
    Excellent,  // 延迟 < 100ms, 带宽 > 10Mbps
    Good,       // 延迟 < 300ms, 带宽 > 5Mbps  
    Fair,       // 延迟 < 800ms, 带宽 > 1Mbps
    Poor        // 延迟 > 800ms 或 带宽 < 1Mbps
}

/**
 * 流客户端状态提供者接口
 * 
 * 允许流客户端（如AdaptiveStreamClient）向网络状态监控器报告连接状态
 */
interface StreamClientStateProvider {
    /**
     * 当前连接状态
     */
    val connectionState: StateFlow<ConnectionState>

    /**
     * 通知连接状态变化
     */
    fun notifyConnectionStateChange(state: ConnectionState)

    /**
     * 开始状态监控
     */
    fun startStateMonitoring()

    /**
     * 停止状态监控
     */
    fun stopStateMonitoring()
}

/**
 * 连接状态事件
 * 
 * 用于在组件间传递连接状态变化事件
 */
sealed interface ConnectionStateEvent {
    val timestamp: Long get() = System.currentTimeMillis()
    val connectionState: ConnectionState

    data class StateChanged(
        override val connectionState: ConnectionState,
        val previousState: ConnectionState? = null
    ) : ConnectionStateEvent

    data class QualityUpdated(
        override val connectionState: ConnectionState,
        val latencyMs: Long,
        val bandwidthKbps: Int
    ) : ConnectionStateEvent

    data class RetryScheduled(
        override val connectionState: ConnectionState,
        val delayMs: Long,
        val attempt: Int
    ) : ConnectionStateEvent
}