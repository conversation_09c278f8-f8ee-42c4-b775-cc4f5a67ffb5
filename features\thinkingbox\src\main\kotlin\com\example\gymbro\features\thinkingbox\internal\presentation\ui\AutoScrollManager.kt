package com.example.gymbro.features.thinkingbox.internal.presentation.ui

import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.input.pointer.pointerInput
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * AutoScrollManager - 自动滚动管理器
 *
 * 🎯 【v2方案】核心功能：
 * - 自动滚动逻辑：当用户在中间50%区域时自动滚动到底部
 * - 用户交互检测：检测滚动、拖拽、按键等操作
 * - 滚动状态管理：autoMode开关和滚动位置跟踪
 *
 * @param listState LazyListState用于滚动控制
 */
class AutoScrollManager(
    private val listState: LazyListState,
) {
    // 🔥 【v2方案】自动滚动模式状态
    var autoMode by mutableStateOf(true)
        private set

    // 🔥 【v2方案】用户交互检测状态
    private var lastUserInteractionTime by mutableLongStateOf(0L)

    /**
     * 判断是否应该自动滚动
     *
     * 🔥 【v2方案】算法：autoMode && 当前可见项目超过总数的50%
     *
     * @return 是否应该自动滚动
     */
    fun shouldAutoScroll(): Boolean {
        val layoutInfo = listState.layoutInfo
        val visibleItems = layoutInfo.visibleItemsInfo
        val totalItems = layoutInfo.totalItemsCount

        if (!autoMode || totalItems == 0) {
            return false
        }

        // 🔥 【v2方案】检查是否在中间50%区域
        val lastVisibleIndex = visibleItems.lastOrNull()?.index ?: 0
        val isInMiddle50Percent = lastVisibleIndex > (totalItems * 0.5)

        Timber.tag(
            "AUTO_SCROLL",
        ).v(
            "📊 [shouldAutoScroll] autoMode=$autoMode, lastVisible=$lastVisibleIndex, total=$totalItems, inMiddle50%=$isInMiddle50Percent",
        )

        return isInMiddle50Percent
    }

    /**
     * 检查是否已经滚动到底部
     *
     * @return 是否在底部
     */
    fun isAtBottom(): Boolean {
        val layoutInfo = listState.layoutInfo
        val visibleItems = layoutInfo.visibleItemsInfo
        val totalItems = layoutInfo.totalItemsCount

        if (totalItems == 0) return true

        val lastVisibleIndex = visibleItems.lastOrNull()?.index ?: 0
        val isAtBottom = lastVisibleIndex >= totalItems - 1

        return isAtBottom
    }

    /**
     * 用户交互回调
     *
     * 🔥 【719施工方案 4.4】手动滚动即暂停自动滚动增强逻辑
     * 当检测到用户滚动、拖拽、按键等操作时调用
     */
    fun onUserInteraction() {
        val currentTime = System.currentTimeMillis()
        lastUserInteractionTime = currentTime

        if (autoMode) {
            autoMode = false
            Timber.tag("TB-AUTO-SCROLL").d("🚫 [onUserInteraction] 用户交互检测，关闭自动滚动模式")

            // 🔥 【719施工方案优化】记录用户交互类型，用于分析用户行为
            Timber.tag("TB-AUTO-SCROLL").i("📊 [UserBehavior] 用户手动滚动，时间戳: $currentTime")
        }
    }

    /**
     * 滚动到底部（如果需要）
     *
     * 🔥 【v2方案】在token添加时调用，根据shouldAutoScroll()决定是否滚动
     *
     * @param scope 协程作用域
     */
    fun scrollToBottomIfNeeded(scope: CoroutineScope) {
        if (shouldAutoScroll()) {
            val totalItems = listState.layoutInfo.totalItemsCount
            if (totalItems > 0) {
                scope.launch {
                    try {
                        // 🔥 【关键修复】适配reverseLayout=true
                        // 在反序布局中，index=0是最新内容（底部），index越大越靠上
                        val targetIndex = 0 // 反序布局中的"底部"
                        listState.animateScrollToItem(targetIndex)
                        Timber.tag(
                            "TB-AUTO-SCROLL",
                        ).v("📜 [反序滚动] 自动滚动到底部: index=$targetIndex (reverseLayout)")
                    } catch (e: Exception) {
                        Timber.tag("TB-AUTO-SCROLL").w("⚠️ [scrollToBottomIfNeeded] 滚动失败: ${e.message}")
                    }
                }
            }
        }
    }

    /**
     * 强制滚动到底部
     *
     * 🔥 【v2方案】ScrollToBottomBtn点击时调用
     *
     * @param scope 协程作用域
     * @param resumeAutoMode 是否恢复自动滚动模式
     */
    fun scrollToBottom(scope: CoroutineScope, resumeAutoMode: Boolean = true) {
        val totalItems = listState.layoutInfo.totalItemsCount
        if (totalItems > 0) {
            scope.launch {
                try {
                    // 🔥 【关键修复】适配reverseLayout=true
                    // 在反序布局中，index=0是最新内容（底部）
                    val targetIndex = 0 // 反序布局中的"底部"
                    listState.animateScrollToItem(targetIndex)

                    if (resumeAutoMode) {
                        autoMode = true
                        Timber.tag("TB-AUTO-SCROLL").d("✅ [反序强制滚动] 强制滚动到底部并恢复自动模式: index=$targetIndex")
                    } else {
                        Timber.tag("TB-AUTO-SCROLL").d("📜 [反序强制滚动] 强制滚动到底部: index=$targetIndex")
                    }
                } catch (e: Exception) {
                    Timber.tag("TB-AUTO-SCROLL").w("⚠️ [反序强制滚动] 滚动失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 重置自动滚动模式
     *
     * 用于新对话开始时重置状态
     */
    fun resetAutoMode() {
        autoMode = true
        lastUserInteractionTime = 0L
        Timber.tag("TB-AUTO-SCROLL").d("🔄 [resetAutoMode] 重置自动滚动模式")
    }

    /**
     * 暂停自动滚动模式
     *
     * 🔥 【用户最佳阅读体验】画布重置期间暂停自动滚动
     * 用于用户消息发送后的画布重置阶段
     */
    fun pauseAutoMode() {
        autoMode = false
        Timber.tag("TB-AUTO-SCROLL").d("⏸️ [pauseAutoMode] 暂停自动滚动模式 - 画布重置中")
    }

    /**
     * 恢复自动滚动模式
     *
     * 🔥 【用户最佳阅读体验】画布重置完成后恢复自动滚动
     * 允许用户交互后恢复正常的滚动行为
     */
    fun resumeAutoMode() {
        autoMode = true
        Timber.tag("TB-AUTO-SCROLL").d("▶️ [resumeAutoMode] 恢复自动滚动模式 - 画布重置完成")
    }
}

/**
 * 用户交互检测的Modifier扩展
 *
 * 🔥 【v2方案】附加到LazyColumn上，检测用户的滚动和拖拽操作
 *
 * @param autoScrollManager AutoScrollManager实例
 * @return 配置了交互检测的Modifier
 */
fun Modifier.detectUserInteraction(
    autoScrollManager: AutoScrollManager,
): Modifier = this
    .nestedScroll(object : NestedScrollConnection {
        override fun onPreScroll(available: Offset, source: NestedScrollSource): Offset {
            // 检测到滚动操作
            if (source == NestedScrollSource.Drag || source == NestedScrollSource.Fling) {
                autoScrollManager.onUserInteraction()
            }
            return Offset.Zero
        }
    })
    .pointerInput(Unit) {
        detectDragGestures(
            onDragStart = {
                // 检测到拖拽开始
                autoScrollManager.onUserInteraction()
            },
            onDrag = { _, _ ->
                // 拖拽过程中持续检测
                // 这里不需要重复调用onUserInteraction，onDragStart已经处理
            },
        )
    }

/**
 * 创建AutoScrollManager的Composable函数
 *
 * @param listState LazyListState
 * @return AutoScrollManager实例
 */
@Composable
fun rememberAutoScrollManager(
    listState: LazyListState,
): AutoScrollManager {
    return remember(listState) {
        AutoScrollManager(listState)
    }
}
