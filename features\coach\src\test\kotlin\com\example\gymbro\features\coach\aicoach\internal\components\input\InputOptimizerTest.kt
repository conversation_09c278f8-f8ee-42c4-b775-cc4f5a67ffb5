package com.example.gymbro.features.coach.aicoach.internal.components.input

import android.content.Context
import com.example.gymbro.features.coach.shared.utils.GlobalPerformanceConfig
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * InputOptimizer单元测试
 *
 * 测试范围：
 * 1. 自适应延迟计算逻辑
 * 2. TextOptimizer性能建议
 * 3. 状态管理和监控
 * 4. 边界条件处理
 *
 * 符合MVI 2.0测试标准：
 * - 纯函数测试覆盖率 100%
 * - 边界条件全覆盖
 * - 性能要求验证
 */
@RunWith(MockitoJUnitRunner::class)
class InputOptimizerTest {

    @Mock
    private lateinit var mockContext: Context

    private lateinit var inputOptimizer: InputOptimizer

    @Before
    fun setUp() {
        inputOptimizer = InputOptimizer(mockContext)
    }

    // ========================================
    // 自适应延迟计算测试
    // ========================================

    @Test
    fun `calculateAdaptiveDelay - 短文本返回快速延迟`() {
        val shortTextLength = 50 // < SHORT_TEXT_THRESHOLD (300)
        val baseDelay = 200L
        
        val actualDelay = inputOptimizer.calculateAdaptiveDelay(shortTextLength, baseDelay)
        val expectedDelay = baseDelay / 4 // 50ms
        
        assertEquals(expectedDelay, actualDelay)
    }

    @Test
    fun `calculateAdaptiveDelay - 中等文本返回标准延迟`() {
        val mediumTextLength = 400 // 介于SHORT和MEDIUM之间
        val baseDelay = 200L
        
        val actualDelay = inputOptimizer.calculateAdaptiveDelay(mediumTextLength, baseDelay)
        
        assertEquals(baseDelay, actualDelay)
    }

    @Test
    fun `calculateAdaptiveDelay - 大文本返回较长延迟`() {
        val largeTextLength = 800 // 介于MEDIUM和LARGE之间
        val baseDelay = 200L
        
        val actualDelay = inputOptimizer.calculateAdaptiveDelay(largeTextLength, baseDelay)
        val expectedDelay = (baseDelay * GlobalPerformanceConfig.Input.ADAPTIVE_DELAY_MULTIPLIER_MEDIUM).toLong()
        
        assertEquals(expectedDelay, actualDelay)
    }

    @Test
    fun `calculateAdaptiveDelay - 超大文本返回最长延迟`() {
        val extraLargeTextLength = 1500 // > LARGE_TEXT_THRESHOLD (1000)
        val baseDelay = 200L
        
        val actualDelay = inputOptimizer.calculateAdaptiveDelay(extraLargeTextLength, baseDelay)
        val expectedDelay = (baseDelay * GlobalPerformanceConfig.Input.ADAPTIVE_DELAY_MULTIPLIER_LARGE).toLong()
        
        assertEquals(expectedDelay, actualDelay)
    }

    @Test
    fun `calculateAdaptiveDelay - 边界值测试`() {
        val baseDelay = 100L
        
        // 边界值：SHORT_TEXT_THRESHOLD
        val shortBoundary = GlobalPerformanceConfig.Input.SHORT_TEXT_THRESHOLD
        val shortDelay = inputOptimizer.calculateAdaptiveDelay(shortBoundary, baseDelay)
        assertEquals(baseDelay, shortDelay) // 等于阈值时使用标准延迟
        
        // 边界值：MEDIUM_TEXT_THRESHOLD
        val mediumBoundary = GlobalPerformanceConfig.Input.MEDIUM_TEXT_THRESHOLD
        val mediumDelay = inputOptimizer.calculateAdaptiveDelay(mediumBoundary, baseDelay)
        assertEquals(baseDelay, mediumDelay)
        
        // 边界值：LARGE_TEXT_THRESHOLD
        val largeBoundary = GlobalPerformanceConfig.Input.LARGE_TEXT_THRESHOLD
        val largeDelay = inputOptimizer.calculateAdaptiveDelay(largeBoundary, baseDelay)
        val expectedLargeDelay = (baseDelay * GlobalPerformanceConfig.Input.ADAPTIVE_DELAY_MULTIPLIER_MEDIUM).toLong()
        assertEquals(expectedLargeDelay, largeDelay)
    }

    // ========================================
    // TextOptimizer测试
    // ========================================

    @Test
    fun `analyzeTextPerformance - 短文本性能最佳`() {
        val shortText = "Hello"
        val optimizer = inputOptimizer.analyzeTextPerformance(shortText)
        
        assertFalse(optimizer.isLargeText)
        assertEquals(shortText.length, optimizer.textLength)
        assertEquals(InputOptimizer.PerformanceLevel.OPTIMAL, optimizer.performanceLevel)
        assertEquals(8, optimizer.suggestedMaxLines) // 短文本使用默认行数
        assertFalse(optimizer.shouldUseVirtualization)
    }

    @Test
    fun `analyzeTextPerformance - 中等文本性能良好`() {
        val mediumText = "a".repeat(400) // 中等长度文本
        val optimizer = inputOptimizer.analyzeTextPerformance(mediumText)
        
        assertFalse(optimizer.isLargeText)
        assertEquals(mediumText.length, optimizer.textLength)
        assertEquals(InputOptimizer.PerformanceLevel.GOOD, optimizer.performanceLevel)
        assertFalse(optimizer.shouldUseVirtualization)
    }

    @Test
    fun `analyzeTextPerformance - 大文本需要优化`() {
        val largeText = "a".repeat(1500) // 超过LARGE_TEXT_THRESHOLD
        val optimizer = inputOptimizer.analyzeTextPerformance(largeText)
        
        assertTrue(optimizer.isLargeText)
        assertEquals(largeText.length, optimizer.textLength)
        assertEquals(InputOptimizer.PerformanceLevel.NEEDS_OPTIMIZATION, optimizer.performanceLevel)
        assertTrue(optimizer.suggestedMaxLines < 8) // 大文本减少显示行数
        assertFalse(optimizer.shouldUseVirtualization) // 1500 < 2000 (LARGE_TEXT_THRESHOLD * 2)
    }

    @Test
    fun `analyzeTextPerformance - 超大文本建议虚拟化`() {
        val extraLargeText = "a".repeat(2500) // > LARGE_TEXT_THRESHOLD * 2
        val optimizer = inputOptimizer.analyzeTextPerformance(extraLargeText)
        
        assertTrue(optimizer.isLargeText)
        assertEquals(InputOptimizer.PerformanceLevel.NEEDS_OPTIMIZATION, optimizer.performanceLevel)
        assertTrue(optimizer.shouldUseVirtualization)
    }

    @Test
    fun `TextOptimizer - suggestedDebounceMs计算正确`() {
        val baseDebounce = GlobalPerformanceConfig.SEARCH.DEBOUNCE_MS
        
        // 短文本 < 100
        val shortOptimizer = inputOptimizer.analyzeTextPerformance("a".repeat(50))
        assertEquals(baseDebounce / 2, shortOptimizer.suggestedDebounceMs)
        
        // 中等文本 100-500
        val mediumOptimizer = inputOptimizer.analyzeTextPerformance("a".repeat(200))
        assertEquals(baseDebounce, mediumOptimizer.suggestedDebounceMs)
        
        // 大文本 500-1000
        val largeOptimizer = inputOptimizer.analyzeTextPerformance("a".repeat(800))
        assertEquals(baseDebounce * 2, largeOptimizer.suggestedDebounceMs)
        
        // 超大文本 > 1000
        val extraLargeOptimizer = inputOptimizer.analyzeTextPerformance("a".repeat(1500))
        assertEquals(baseDebounce * 3, extraLargeOptimizer.suggestedDebounceMs)
    }

    @Test
    fun `TextOptimizer - suggestedMaxLines自适应计算`() {
        val threshold = GlobalPerformanceConfig.Input.LARGE_TEXT_THRESHOLD
        
        // 小于阈值的文本
        val smallOptimizer = inputOptimizer.analyzeTextPerformance("a".repeat(500))
        assertEquals(8, smallOptimizer.suggestedMaxLines)
        
        // 刚好超过阈值的文本
        val largeOptimizer = inputOptimizer.analyzeTextPerformance("a".repeat(threshold + 100))
        val expectedMaxLines = kotlin.math.max(4, 20 - ((threshold + 100) / GlobalPerformanceConfig.Input.MEDIUM_TEXT_THRESHOLD))
        assertEquals(expectedMaxLines, largeOptimizer.suggestedMaxLines)
        
        // 超大文本确保最小值为4
        val extraLargeOptimizer = inputOptimizer.analyzeTextPerformance("a".repeat(10000))
        assertTrue(extraLargeOptimizer.suggestedMaxLines >= 4)
    }

    // ========================================
    // 状态管理测试
    // ========================================

    @Test
    fun `getCurrentOptimizationState - 初始状态为false`() {
        assertFalse(inputOptimizer.getCurrentOptimizationState())
    }

    @Test
    fun `isOptimizing StateFlow - 初始值为false`() = runTest {
        val initialState = inputOptimizer.isOptimizing.first()
        assertFalse(initialState)
    }

    // ========================================
    // 性能等级边界测试
    // ========================================

    @Test
    fun `PerformanceLevel - 所有阈值边界正确分类`() {
        val shortThreshold = GlobalPerformanceConfig.Input.SHORT_TEXT_THRESHOLD
        val mediumThreshold = GlobalPerformanceConfig.Input.MEDIUM_TEXT_THRESHOLD
        val largeThreshold = GlobalPerformanceConfig.Input.LARGE_TEXT_THRESHOLD
        
        // 测试各个边界值
        assertEquals(
            InputOptimizer.PerformanceLevel.OPTIMAL,
            inputOptimizer.analyzeTextPerformance("a".repeat(shortThreshold - 1)).performanceLevel
        )
        
        assertEquals(
            InputOptimizer.PerformanceLevel.GOOD,
            inputOptimizer.analyzeTextPerformance("a".repeat(shortThreshold)).performanceLevel
        )
        
        assertEquals(
            InputOptimizer.PerformanceLevel.GOOD,
            inputOptimizer.analyzeTextPerformance("a".repeat(mediumThreshold - 1)).performanceLevel
        )
        
        assertEquals(
            InputOptimizer.PerformanceLevel.MODERATE,
            inputOptimizer.analyzeTextPerformance("a".repeat(mediumThreshold)).performanceLevel
        )
        
        assertEquals(
            InputOptimizer.PerformanceLevel.MODERATE,
            inputOptimizer.analyzeTextPerformance("a".repeat(largeThreshold - 1)).performanceLevel
        )
        
        assertEquals(
            InputOptimizer.PerformanceLevel.NEEDS_OPTIMIZATION,
            inputOptimizer.analyzeTextPerformance("a".repeat(largeThreshold)).performanceLevel
        )
    }

    // ========================================
    // 配置一致性测试
    // ========================================

    @Test
    fun `配置值与GlobalPerformanceConfig一致`() {
        val optimizer = inputOptimizer.analyzeTextPerformance("")
        
        assertEquals(
            GlobalPerformanceConfig.Input.LARGE_TEXT_THRESHOLD,
            optimizer.threshold
        )
    }

    // ========================================
    // 无状态组件测试
    // ========================================

    @Test
    fun `analyzeTextPerformance - 多次调用结果一致`() {
        val text = "a".repeat(500)
        
        val result1 = inputOptimizer.analyzeTextPerformance(text)
        val result2 = inputOptimizer.analyzeTextPerformance(text)
        
        assertEquals(result1.isLargeText, result2.isLargeText)
        assertEquals(result1.textLength, result2.textLength)
        assertEquals(result1.threshold, result2.threshold)
        assertEquals(result1.performanceLevel, result2.performanceLevel)
        assertEquals(result1.suggestedMaxLines, result2.suggestedMaxLines)
        assertEquals(result1.suggestedDebounceMs, result2.suggestedDebounceMs)
        assertEquals(result1.shouldUseVirtualization, result2.shouldUseVirtualization)
    }

    // ========================================
    // 极端情况测试
    // ========================================

    @Test
    fun `极端情况 - 空文本处理`() {
        val optimizer = inputOptimizer.analyzeTextPerformance("")
        
        assertFalse(optimizer.isLargeText)
        assertEquals(0, optimizer.textLength)
        assertEquals(InputOptimizer.PerformanceLevel.OPTIMAL, optimizer.performanceLevel)
        assertEquals(8, optimizer.suggestedMaxLines)
        assertFalse(optimizer.shouldUseVirtualization)
    }

    @Test
    fun `极端情况 - 零延迟基准处理`() {
        val delay = inputOptimizer.calculateAdaptiveDelay(100, 0L)
        assertEquals(0L, delay) // 确保零延迟不会导致异常
    }

    @Test
    fun `极端情况 - 负延迟基准处理`() {
        val delay = inputOptimizer.calculateAdaptiveDelay(100, -100L)
        assertTrue(delay <= 0) // 负延迟保持负值或零
    }
}

/**
 * 扩展函数用于访问私有方法进行测试
 */
private fun InputOptimizer.calculateAdaptiveDelay(textLength: Int, baseDelay: Long): Long {
    // 通过反射访问私有方法
    val method = this::class.java.getDeclaredMethod(
        "calculateAdaptiveDelay",
        Int::class.java,
        Long::class.java
    )
    method.isAccessible = true
    return method.invoke(this, textLength, baseDelay) as Long
}