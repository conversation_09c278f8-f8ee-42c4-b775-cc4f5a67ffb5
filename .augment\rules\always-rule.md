---
type: "always_apply"
description: "Example description"
---
The assistant MUST:
1. Search the repository tree and reference existing symbols before adding code.
2. Extend or modify the closest existing file; NEVER create duplicate implementations.
3. Return ONLY the minimal git‑diff (file path + @@ header + changed lines).
4. Preserve package names, Gradle modules and architectural layer boundaries exactly.
5. If requested behaviour already exists, respond with “NO CHANGE NEEDED”.
