package com.example.gymbro.features.thinkingbox.internal.presentation.contract

import androidx.compose.runtime.Immutable
import com.example.gymbro.core.arch.mvi.AppIntent
import com.example.gymbro.core.arch.mvi.UiEffect
import com.example.gymbro.core.arch.mvi.UiState
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi

/**
 * ThinkingBox MVI Contract
 * 定义Intent、State、Effect的完整契约
 */
object ThinkingBoxContract {

    /**
     * UI状态 - 不可变数据类
     */
    @Immutable
    data class State(
        // 核心状态
        val messageId: String = "",
        val phases: List<PhaseUi> = emptyList(), // UI 消费的 phase 列表
        val activePhaseId: String? = null, // 当前活跃 phase
        val preThinking: String? = null, // perthink 文本内容

        // 流式状态
        val isStreaming: Boolean = false,
        val isThinkingComplete: Boolean = false,

        // Final 相关
        val finalMarkdown: String? = null, // 最终富文本内容
        val finalTokens: List<String> = emptyList(), // Final token 列表 (TypewriterRenderer)
        val finalRichTextReady: Boolean = false, // StreamingFinalRenderer 就绪
        val finalContentArrived: Boolean = false, // Final 数据已到达
        val isFinalStreaming: Boolean = false, // Final 流式状态

        // UI 控制
        val summaryTextVisible: Boolean = false,
        val showSummaryPanel: Boolean = false,
        val isFinalRenderingComplete: Boolean = false,

        // 统计信息
        val thinkingDuration: Long = 0L,
        val totalTokens: Int = 0,

        // 错误状态
        val error: UiText? = null,
        val isLoading: Boolean = false,
    ) : UiState {
        /**
         * 计算是否有实际思考内容
         */
        val hasActualThinkingContent: Boolean
            get() = phases.isNotEmpty() || !preThinking.isNullOrBlank()

        /**
         * 计算是否应该显示final文本
         */
        val shouldShowFinalText: Boolean
            get() = finalRichTextReady && (finalTokens.isNotEmpty() || !finalMarkdown.isNullOrBlank())

        /**
         * 计算是否应该显示ThinkingHeader
         */
        val shouldShowThinkingHeader: Boolean
            get() = when {
                phases.isNotEmpty() -> false
                isThinkingComplete -> false
                shouldShowFinalText -> false
                !preThinking.isNullOrBlank() -> true // 修复：有 preThinking 时显示 header
                else -> isStreaming
            }
    }

    /**
     * 用户意图
     */
    sealed interface Intent : AppIntent {
        // 生命周期意图
        data class Initialize(val messageId: String) : Intent
        data object Reset : Intent

        // 事件处理意图
        data class HandleThinkingEvent(
            val event: com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent,
        ) : Intent

        // UI交互意图
        data class PhaseAnimationFinished(val phaseId: String) : Intent
        data object ToggleSummaryPanel : Intent
        data object CompleteSummaryAnimation : Intent
        data object CompleteFinalRendering : Intent

        // 错误处理意图
        data object ClearError : Intent
    }

    /**
     * 副作用
     */
    sealed interface Effect : UiEffect {
        // 导航效果
        data object ScrollToBottom : Effect

        // 外部通信效果
        data class SendEventToExternal(
            val event: com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent,
        ) : Effect

        data class NotifyMessageComplete(
            val messageId: String,
            val finalMarkdown: String,
        ) : Effect

        // 错误效果
        data class ShowError(val error: UiText) : Effect

        // 日志效果
        data class LogDebug(val message: String) : Effect
    }
}
