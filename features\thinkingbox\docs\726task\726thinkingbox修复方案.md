Here's a concrete refactor plan that answers each open question and gives you a clean, testable pipeline.

---

## 1. Put **StringXmlEscaper** at the boundary (core-network)

* Do the unsafe → safe XML conversion **before** tokens ever hit ThinkingBox. AdaptiveStreamClient already injects `<think>`/`</think>` and calls `StringXmlEscaper.sanitizeAndEscape(...)`. &#x20;
* The class itself is documented as “输入验证在边界：在 core-network 层确保输出安全；单一职责：XML 安全处理，不涉及业务逻辑”。&#x20;

> **Decision**: **Keep StringXmlEscaper in `core-network` (AdaptiveStreamClient)**. ThinkingBox consumes already-clean XML; it must not re‑escape or “fix” tokens.

---

## 2. Single, ordered token pipeline

**Final path** (no duplicates):

```
AdaptiveStreamClient(JSON SSE)
  → StringXmlEscaper (clean XML)
  → TokenBus
  → TokenRouter
  → ConversationScope.tokens
  → ThinkingBoxViewModel
  → StreamingThinkingMLParser (syntax → SemanticEvents)
  → DomainMapper (semantics → ThinkingEvents)
  → ThinkingReducer (state machine)
  → Contract.State
  → ThinkingBoxScreen/UI
```

This matches the v3.0 doc’s “ConversationScope → ViewModel → Parser → DomainMapper → Reducer → State → UI” line.&#x20;
And the mermaid showing Coach→OpenAI→Token流→ConversationScope→ViewModel→Parser→DomainMapper→Reducer→State→UI.&#x20;

---

## 3. Component responsibilities (no overlap)

* **AdaptiveStreamClient + StringXmlEscaper**: Detect JSON vs text, inject `<think>` context, escape/clean illegal tags (`<phase:PLAN>` etc.). &#x20;
* **TokenBus / TokenRouter / ConversationScope**: Transport and multiplex clean XML tokens to the right message scope. ViewModel only subscribes to `ConversationScope.tokens` (no direct TokenBus subscribe).&#x20;
* **RawChunkProcessor** (rename or delete?): Any remaining *raw* preprocessing should happen **only** here, but if Escaper already guarantees XML, you can retire RawChunkProcessor or keep it to glue legacy paths. (No direct citation needed since this is guidance; keep it minimal.)
* **XmlStreamScanner**: Low-level tokenization of XML stream; no semantic knowledge.&#x20;
* **StreamingThinkingMLParser**: Pure syntax parser → emits `TagOpened`, `TagClosed`, `TextChunk`, etc. Maintains only minimal parser state (PRE\_THINK / THINKING / POST\_FINAL).&#x20;
* **DomainMapper**: Map SemanticEvents to ThinkingEvents (phase start/end/title, final start/end, etc.).&#x20;
* **ThinkingMLGuardrail**: Validate/repair malformed sequences (optional: after DomainMapper, before Reducer). Keep it to semantic-level fixes only. (If not in docs, treat as future guardrail.)
* **ThinkingReducer**: Single source of truth for UI state, implements dual-handshake (data complete + animation complete).&#x20;
* **ThinkingBoxViewModel**: Subscribes once, drives parser + mapper, updates Contract.State, emits Effects.&#x20;
* **ThinkingBoxScreen/UI**: Pure rendering + animation. Sends `PhaseAnimationFinished` to complete the handshake.&#x20;

---

## 4. Token format transformation rules

| Input (JSON SSE fields)               | Boundary action                                                     | TB semantic result                                             |
| ------------------------------------- | ------------------------------------------------------------------- | -------------------------------------------------------------- |
| `reasoning_content` chunk             | Escape + wrap in `<think>` once; subsequent chunks append text only | Phase `perthink` content grows until `<thinking>`              |
| `content` first chunk after reasoning | Prepend `</think>` once, then normal text                           | Closes perthink, opens formal thinking if `<thinking>` arrives |
| `<phase:PLAN>…</phase:PLAN>` etc      | Strip illegal tag, keep text                                        | Remains inside current phase (usually perthink)                |

Rules are in the doc: perthink starts immediately, closed by `<thinking>`; final kicks off background render on `<final>`, front-end render waits until thinking box closes.&#x20;

Additional state toggles (final flags, etc.) are in Contract.State.&#x20;

---

## 5. Dual-timeline (“双时序”) & handshake

* Data timeline: Parser/Mapper/Reducer mark `phase.isComplete=true` on `</phase>`.
* UI timeline: UI finishes animation and emits `PhaseAnimFinished`. Only then do we advance to next phase / close box / show final.&#x20;

---

## 6. Coach ↔ ThinkingBox contract

Coach module only:

1. Sends user msg → gets `messageId`.
2. Hosts the ThinkingBox composable and passes `onDataReady` to persist finalMarkdown/history.&#x20;
3. Never touches internal TB state; no shared state.&#x20;

ThinkingBox:

* Emits `NotifyMessageComplete(messageId, finalMarkdown)` effect when done.&#x20;
* Prevent duplicate callbacks with local guard (as shown in Coach side).&#x20;

---

## 7. Step-by-step refactor checklist

1. **Network boundary**

   * Ensure `AdaptiveStreamClient.parseJsonContentWithContext` is the only place injecting `<think>`/`</think>` and cleaning tags.&#x20;
   * Remove duplicate preprocessing in TB VM (already noted).&#x20;

2. **Wire the Token flow**

   * TokenBus → TokenRouter → ConversationScope; ViewModel listens to `conversationScope.tokens`.&#x20;
   * Delete any lingering direct TokenBus subscriptions in TB.&#x20;

3. **Parser isolation**

   * Keep `parseTokenStream(...)` for backward compatibility (dual interface).&#x20;

4. **Reducer & UI handshake**

   * Verify `PhaseAnimFinished` path updates `finalRichTextReady` only once when pending is empty & final arrived. (See reducer snippet.)&#x20;

5. **Contract.State cleanup**

   * Use existing computed flags (`shouldShowFinalText`, etc.) to drive visibility; no UI logic in VM.&#x20;

6. **Logging & diagnostics**

   * Keep `TB-RAW-TOKEN` / `UI-DEBUG` logs for now; strip before release. &#x20;

---

## 8. Mermaid overview (drop-in to docs)

```mermaid
graph TD
    subgraph Core-Network
      A(JSON SSE)
      B[StringXmlEscaper]
      A --> B --> C[TokenBus]
    end
    C --> D[TokenRouter]
    D --> E[ConversationScope.tokens]

    subgraph ThinkingBox
      E --> F[ThinkingBoxViewModel]
      F --> G[StreamingThinkingMLParser]
      G --> H[DomainMapper]
      H --> I[ThinkingReducer]
      I --> J[Contract.State]
      J --> K[ThinkingBoxScreen/UI]
    end

    K -->|PhaseAnimFinished| F
    I -->|NotifyMessageComplete| Coach( Coach Module )
```

---
很好，这套规则已经把“唯一责任 + 明确状态切换”说清了。我建议再补齐三块：**状态机定义、事件→动作表、单测清单**。下面给出精炼版实现建议。

---

## ✅ 标签与状态唯一性（最终版）

### 状态机（Parser/DomainMapper层共有的语义状态）

* `PERTHINK`：流开始即进入，直到遇到 `<thinking>`。
* `THINKING`：`<thinking>` 至 `</thinking>`，内部按 `<phase id>` 递增管理。
* `FINAL`：遇到 `<final>` 后开始后台渲染；UI需等待思考框关闭信号再展示。

> `id` 动态生成即可，不做强制规则；但必须**唯一且递增**，保证 UI 的 diff/动画稳定。

### 事件 → 动作（简表）

| 输入事件（标签/流式信号）                  | 当前状态      | 动作                                        | 下一个状态                             |
| ------------------------------ | --------- | ----------------------------------------- | --------------------------------- |
| **StreamStart**                | (none)    | `phase = perthink` 建立；openPhase(perthink) | PERTHINK                          |
| `<phase:PLAN>`…`</phase:PLAN>` | PERTHINK  | 去标签保内容 → appendTo(perthink)               | PERTHINK                          |
| `<thinking>`                   | PERTHINK  | closePhase(perthink); openThinkingBox()   | THINKING                          |
| `<phase id="X">`               | THINKING  | openPhase(X)；更新标题(如有)                     | THINKING                          |
| `</phase>`                     | THINKING  | markPhaseComplete(X); 发出“数据完成”半握手         | THINKING                          |
| `</thinking>`                  | THINKING  | closeAllPhases(); markThinkingDone        | THINKING → (waiting for UI close) |
| `<final>`                      | ANY       | startBackgroundFinalRender()              | FINAL (数据线)                       |
| UI: PhaseAnimFinished(X)       | THINKING  | if dataDone(X) → advanceToNextPhase()     | THINKING                          |
| UI: ThinkingBoxClosed          | FINAL数据就绪 | showFinal()                               | (END)                             |

---

## 🧱 模块职责再确认

* **StringXmlEscaper（core-network）**

  * 只做：非法字符/标签转义、`<phase:PLAN>` → 文本。
  * ThinkingBox 不再“逃逸修补”。

* **StreamingThinkingMLParser**

  * 只负责把 XML 结构流转成语法事件：OpenTag/CloseTag/TextChunk。
  * 不关心 `phase id` 语义、UI。

* **DomainMapper**

  * 把语法事件映射为语义事件（PhaseStart/PhaseEnd/FinalStart…）。
  * 确定 `perthink`、`phase id` 分配规则。

* **ThinkingMLGuardrail（可选）**

  * 专门做“序列修复”：丢失的 `</phase>`、乱序标签等。不要和 Escaper 职责重叠。

* **Reducer / State**

  * 统一状态机 + 双时序握手。UI 不做逻辑判断，只根据 `Contract.State` 渲染。

---

## 🧪 最小化测试用例集

1. **纯 reasoning\_content，无 `<think>`**

   * 期望：自动 perthink，相遇 `<thinking>` 后关闭 perthink。

2. **存在 `<phase:PLAN>`**

   * 期望：标签被清理，文本保留在 perthink；无 XML 异常。

3. **缺失 `</phase>` 的流**

   * Guardrail 自动补齐；UI 不卡死。

4. **`<final>` 提前到来（thinking 未结束）**

   * 后台渲染等待思考框关闭再显示。

5. **空标题 `<title></title>`**

   * DomainMapper 允许空标题，不影响 Phase 刷新。

6. **重复 `<phase id="1">`**

   * Mapper 重写 id 或报错（根据你策略），UI 只看到唯一递增 id。

---

## 🔧 实现要点/建议

* **ID 分配策略**：

  * `perthink` 固定别名；THINKING 阶段用 `nextPhaseId++`。不要依赖服务端传入 id。

* **流式拼接策略**：

  * TextChunk 合并策略在 DomainMapper 层做（避免 UI 收到碎片太多）。

* **UI 双握手的判定点**：

  * “数据完成”= DomainMapper 发出 PhaseEnd。
  * “动画完成”= UI 发 `PhaseAnimFinished`。Reducer 同时收到才推进。

* **最终富文本**：

  * `StreamingFinalRenderer` 在 `<final>` 时开始接收；Reducer 控制可见性。

---

需要我把上面内容整理成代码框架/接口签名或 Mermaid 状态图，或直接给你单测模板、Reducer 伪代码吗？告诉我下一步你想要的交付形式即可。💡
