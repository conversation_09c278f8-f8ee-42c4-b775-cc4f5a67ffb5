package com.example.gymbro.features.workout.shared.components.exercise

import androidx.compose.runtime.Stable
import com.example.gymbro.shared.models.exercise.ExerciseDto
import com.example.gymbro.shared.models.exercise.ExerciseSetDto

/**
 * 训练动作组件状态 - 智能自适应版本
 *
 * 支持COMPACT和EXPANDED两种显示模式，实现智能的状态切换和显示逻辑
 */
@Stable
data class WorkoutExerciseState(
    // === 原有字段 ===
    val exercise: ExerciseDto,
    val currentSets: List<ExerciseSetDto>,
    val editingSetId: String? = null,

    // === 新增：智能显示控制 ===
    val displayMode: ExerciseDisplayMode = ExerciseDisplayMode.COMPACT,
    val autoCollapseOnComplete: Boolean = false, // Session中默认不自动收起
    val allowManualToggle: Boolean = true,

    // === UI刷新控制 ===
    val lastRestTimeUpdate: Long = 0L, // 用于强制REST_TIME UI刷新
) {
    // === 计算属性 ===

    /**
     * 所有组是否已完成
     */
    val allSetsCompleted: Boolean
        get() = currentSets.isNotEmpty() && currentSets.all { it.isCompleted }

    /**
     * 完成进度（0.0 - 1.0）
     */
    val completionProgress: Float
        get() = if (currentSets.isEmpty()) {
            0f
        } else {
            currentSets.count { it.isCompleted }.toFloat() / currentSets.size
        }

    /**
     * COMPACT状态显示文本
     * 未完成：显示剩余组数
     * 完成：显示统计数据
     */
    val compactDisplayText: String
        get() = if (allSetsCompleted) {
            // 完成状态：显示统计
            val completedSets = currentSets.filter { it.isCompleted }
            if (completedSets.isNotEmpty()) {
                val avgWeight = completedSets.map { it.weight }.average()
                val totalSets = completedSets.size
                "已完成 ${String.format("%.1f", avgWeight)}kg x ${totalSets}组"
            } else {
                "已完成"
            }
        } else {
            // 未完成状态：显示剩余组数
            val remaining = currentSets.count { !it.isCompleted }
            "剩余 ${remaining}组"
        }

    /**
     * EXPANDED状态统计显示
     * 格式：已完成 重量x次数 / 目标 重量x次数
     */
    val completedStats: String
        get() {
            val completed = currentSets.filter { it.isCompleted }
            val total = currentSets.size
            return if (completed.isNotEmpty()) {
                val lastCompleted = completed.last()
                "${lastCompleted.weight}kg x ${lastCompleted.reps} / ${total}组已完成"
            } else {
                "0 / ${total}组已完成"
            }
        }

    /**
     * 是否可以添加新组
     * 🔥 修复：支持最多50组的组数扩展，确保用户可以自定义组数
     */
    val canAddSet: Boolean get() = currentSets.size < 50 // 支持最多50组，符合任务要求

    /**
     * 🔥 新增：检查是否达到组数上限
     */
    val isAtMaxSets: Boolean get() = currentSets.size >= 50

    /**
     * 🔥 新增：获取可添加的剩余组数
     */
    val remainingSets: Int get() = maxOf(0, 50 - currentSets.size)
}

/**
 * 训练动作组件Intent - 智能自适应版本
 *
 * 扩展了显示模式控制的Intent，支持COMPACT和EXPANDED状态切换
 */
sealed interface WorkoutExerciseIntent {
    // === 原有Intent ===
    data class UpdateWeight(
        val setId: String,
        val weight: Float,
    ) : WorkoutExerciseIntent

    data class UpdateReps(
        val setId: String,
        val reps: Int,
    ) : WorkoutExerciseIntent

    data class ToggleComplete(
        val setId: String,
    ) : WorkoutExerciseIntent

    data class AddSet(
        val copyFromSetId: String? = null,
    ) : WorkoutExerciseIntent

    data class RemoveSet(
        val setId: String,
    ) : WorkoutExerciseIntent

    // === 新增：显示模式控制 ===

    /**
     * 切换到指定的显示模式
     */
    data class ChangeDisplayMode(
        val mode: ExerciseDisplayMode,
    ) : WorkoutExerciseIntent

    /**
     * 在COMPACT和EXPANDED之间切换
     */
    object ToggleDisplayMode : WorkoutExerciseIntent
}
