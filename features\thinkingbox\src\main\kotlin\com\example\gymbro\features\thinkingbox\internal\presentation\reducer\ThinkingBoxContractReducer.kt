package com.example.gymbro.features.thinkingbox.internal.presentation.reducer

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.domain.reducer.ThinkingReducer
import com.example.gymbro.features.thinkingbox.internal.presentation.contract.ThinkingBoxContract
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ThinkingBox Contract Reducer - MVI 2.0 合规版本
 *
 * 🎯 核心职责：
 * - 处理所有 Contract Intent，实现完整的状态转换逻辑
 * - 集成 Domain 层的 ThinkingReducer，避免 ViewModel 中的复杂逻辑
 * - 提供统一的状态管理，遵循"单一真源"原则
 *
 * 🔥 MVI 2.0 架构原则：
 * - Reducer 承担主要的状态转换职责
 * - ViewModel 只负责协调和效果处理
 * - 状态转换逻辑集中在 Reducer 中
 */
@Singleton
class ThinkingBoxContractReducer @Inject constructor() : Reducer<ThinkingBoxContract.Intent, ThinkingBoxContract.State, ThinkingBoxContract.Effect> {

    override fun reduce(
        intent: ThinkingBoxContract.Intent,
        currentState: ThinkingBoxContract.State,
    ): ReduceResult<ThinkingBoxContract.State, ThinkingBoxContract.Effect> {
        return when (intent) {
            is ThinkingBoxContract.Intent.Initialize -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        messageId = intent.messageId,
                        isLoading = true,
                        error = null,
                    ),
                )
            }

            is ThinkingBoxContract.Intent.Reset -> {
                ReduceResult.stateOnly(
                    ThinkingBoxContract.State(messageId = currentState.messageId),
                )
            }

            is ThinkingBoxContract.Intent.PhaseAnimationFinished -> {
                // 此 intent 会被转发到 domain 层处理
                ReduceResult.stateOnly(currentState)
            }

            is ThinkingBoxContract.Intent.ToggleSummaryPanel -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        showSummaryPanel = !currentState.showSummaryPanel,
                    ),
                )
            }

            is ThinkingBoxContract.Intent.CompleteSummaryAnimation -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        summaryTextVisible = true,
                    ),
                )
            }

            is ThinkingBoxContract.Intent.CompleteFinalRendering -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        isFinalRenderingComplete = true,
                    ),
                )
            }

            is ThinkingBoxContract.Intent.ClearError -> {
                ReduceResult.stateOnly(
                    currentState.copy(error = null),
                )
            }

            is ThinkingBoxContract.Intent.HandleThinkingEvent -> {
                // 🔥 MVI 2.0 修复：在 Reducer 中直接处理 Domain 事件
                handleThinkingEventIntent(intent.event, currentState)
            }
        }
    }

    /**
     * 🔥 MVI 2.0 核心改进：在Contract Reducer中处理Domain事件
     *
     * 这个方法将Domain层的ThinkingEvent转换为Contract层的状态更新
     * 避免了ViewModel中的复杂状态转换逻辑
     */
    private fun handleThinkingEventIntent(
        thinkingEvent: ThinkingEvent,
        currentState: ThinkingBoxContract.State,
    ): ReduceResult<ThinkingBoxContract.State, ThinkingBoxContract.Effect> {
        // 将Contract State转换为Domain State进行处理
        val domainState = convertToReducerState(currentState)
        val newDomainState = ThinkingReducer.reduce(domainState, thinkingEvent)

        // 检查状态是否真的发生了变化
        if (newDomainState == domainState) {
            return ReduceResult.stateOnly(currentState)
        }

        // 转换回Contract State
        val newContractState = convertToContractState(newDomainState)

        // 检查是否需要发送Effect
        val effects = mutableListOf<ThinkingBoxContract.Effect>()

        // 🔥 思考完成通知
        if (newDomainState.isThinkingComplete && !newDomainState.finalMarkdown.isNullOrBlank()) {
            effects.add(
                ThinkingBoxContract.Effect.NotifyMessageComplete(
                    currentState.messageId,
                    newDomainState.finalMarkdown,
                ),
            )
        }

        // 🔥 自动滚动效果
        if (thinkingEvent is ThinkingEvent.PhaseContent || thinkingEvent is ThinkingEvent.FinalToken) {
            effects.add(ThinkingBoxContract.Effect.ScrollToBottom)
        }

        return if (effects.isNotEmpty()) {
            ReduceResult.withEffects(newContractState, effects)
        } else {
            ReduceResult.stateOnly(newContractState)
        }
    }

    /**
     * 转换 Contract State 到 Domain Reducer State
     */
    private fun convertToReducerState(
        contractState: ThinkingBoxContract.State,
    ): ThinkingReducer.ThinkingUiState {
        return ThinkingReducer.ThinkingUiState(
            sessionId = contractState.messageId,
            phases = LinkedHashMap(
                contractState.phases.associateBy { it.id }.mapValues {
                    ThinkingReducer.PhaseUi(
                        it.value.id,
                        it.value.title,
                        it.value.content,
                        it.value.isComplete,
                    )
                },
            ),
            activePhaseId = contractState.activePhaseId,
            preThinking = contractState.preThinking,
            isStreaming = contractState.isStreaming,
            isThinkingComplete = contractState.isThinkingComplete,
            finalMarkdown = contractState.finalMarkdown,
            finalTokens = contractState.finalTokens,
            finalRichTextReady = contractState.finalRichTextReady,
            thinkingDuration = contractState.thinkingDuration,
            totalTokens = contractState.totalTokens,
        )
    }

    /**
     * 转换 Domain Reducer State 到 Contract State
     */
    private fun convertToContractState(
        reducerState: ThinkingReducer.ThinkingUiState,
    ): ThinkingBoxContract.State {
        return ThinkingBoxContract.State(
            messageId = reducerState.sessionId ?: "",
            phases = reducerState.phases.values.map {
                com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi(
                    it.id,
                    it.title,
                    it.content,
                    it.isComplete,
                )
            },
            activePhaseId = reducerState.activePhaseId,
            preThinking = reducerState.preThinking,
            isStreaming = reducerState.isStreaming,
            isThinkingComplete = reducerState.isThinkingComplete,
            finalMarkdown = reducerState.finalMarkdown,
            finalTokens = reducerState.finalTokens,
            finalRichTextReady = reducerState.finalRichTextReady,
            thinkingDuration = reducerState.thinkingDuration,
            totalTokens = reducerState.totalTokens,
        )
    }
}