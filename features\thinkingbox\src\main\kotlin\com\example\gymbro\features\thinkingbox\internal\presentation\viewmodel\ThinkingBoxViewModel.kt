package com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel

import com.example.gymbro.core.arch.mvi.BaseMviViewModel
import com.example.gymbro.core.network.router.TokenRouter
import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.domain.reducer.ThinkingReducer
import com.example.gymbro.features.thinkingbox.internal.presentation.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.internal.presentation.reducer.ThinkingBoxContractReducer
import com.example.gymbro.features.thinkingbox.logging.RawTokenRecorder
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class ThinkingBoxViewModel @Inject constructor(
    private val tokenRouter: Token<PERSON>outer,
    private val streamingParser: StreamingThinkingMLParser,
    private val domainMapper: DomainMapper,
    private val tokenizerService: com.example.gymbro.core.ai.tokenizer.TokenizerService,
    private val contractReducer: ThinkingBoxContractReducer,
) : BaseMviViewModel<ThinkingBoxContract.Intent, ThinkingBoxContract.State, ThinkingBoxContract.Effect>(
    initialState = ThinkingBoxContract.State(),
) {

    override val reducer = contractReducer

    private var parseJob: Job? = null
    private var currentMessageId: String? = null

    init {
        initializeEffectHandler()
    }

    override fun initializeEffectHandler() {
        // 监听 Contract Intent 并转换为 Domain 层操作
        handlerScope.launch {
            effect.collect { effect ->
                when (effect) {
                    is ThinkingBoxContract.Effect.NotifyMessageComplete -> {
                        // Effect 已经发送，这里可以做额外的处理
                        Timber.d("Message completed: ${effect.messageId}")
                    }

                    is ThinkingBoxContract.Effect.ScrollToBottom -> {
                        // UI 层会处理这个 effect
                    }

                    is ThinkingBoxContract.Effect.ShowError -> {
                        // UI 层会处理这个 effect
                    }

                    is ThinkingBoxContract.Effect.LogDebug -> {
                        Timber.d(effect.message)
                    }

                    is ThinkingBoxContract.Effect.SendEventToExternal -> {
                        // 可以在这里处理外部事件发送
                    }
                }
            }
        }
    }

    /**
     * 处理 Initialize Intent
     * 启动 token 解析和 thinking 事件处理
     */
    fun handleInitialize(messageId: String) {
        if (currentMessageId == messageId && parseJob?.isActive == true) {
            Timber.tag("TB-VIEWMODEL").d("Already initialized for messageId: $messageId, skipping")
            return
        }

        Timber.tag("TB-VIEWMODEL").e("Initializing ThinkingBoxViewModel for messageId: $messageId")

        currentMessageId = messageId
        parseJob?.cancel()

        // 发送 Initialize Intent
        dispatch(ThinkingBoxContract.Intent.Initialize(messageId))

        val internalState = ThinkingReducer.createInitialState(messageId)
        var mappingContext = DomainMapper.MappingContext()

        parseJob = handlerScope.launch {
            try {
                RawTokenRecorder.activate()
                Timber.tag("TB-RAW-TOKEN").e("TB-RAW-TOKEN标签测试 - 如果看到此日志说明标签配置正确")

                val conversationScope = tokenRouter.getOrCreateScope(messageId)
                Timber.tag("TB-VIEWMODEL").e("ThinkingBoxViewModel已启动，messageId=$messageId")

                streamingParser.parseTokenStream(
                    messageId = messageId,
                    tokens = conversationScope.tokens,
                    onEvent = { semanticEvent ->
                        val mappingResult = domainMapper.mapSemanticToThinking(semanticEvent, mappingContext)
                        mappingContext = mappingResult.context
                        mappingResult.events.forEach { thinkingEvent ->
                            processThinkingEvent(thinkingEvent)
                        }
                    },
                )
            } catch (e: Exception) {
                Timber.tag("TB-VIEWMODEL").e(e, "Failed to initialize token parsing for messageId: $messageId")
            }
        }
    }

    /**
     * 🔥 MVI 2.0 简化版：处理 ThinkingEvent 并委托给 Reducer
     *
     * ViewModel 不再包含复杂的状态转换逻辑，
     * 而是直接通过 dispatch Intent 让 Reducer 处理
     */
    private fun processThinkingEvent(event: ThinkingEvent) {
        // 🔥 【关键简化】直接发送Intent给Reducer处理，移除复杂的状态转换逻辑
        dispatch(ThinkingBoxContract.Intent.HandleThinkingEvent(event))

        // 简单的日志记录
        Timber.tag("TB-VIEWMODEL").v("已委托处理ThinkingEvent: ${event::class.simpleName}")
    }

    override fun onCleared() {
        super.onCleared()
        RawTokenRecorder.deactivate()
        parseJob?.cancel()
        Timber.tag("TB-VIEWMODEL").d("ThinkingBoxViewModel已清理，RAW TOKEN记录器已停用")
    }
}